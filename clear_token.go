package main

import (
	"fmt"
	"log"

	"github.com/privasea/motivus/config"
	"github.com/privasea/motivus/models"
)

func main() {
	// Load configuration
	if err := config.LoadConfig("api/config/config.yaml"); err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// Initialize database
	if err := models.InitDB(); err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// Clear token for develop cluster
	result := models.DB.Model(&models.Cluster{}).Where("name = ?", "develop").Update("token", "")
	if result.Error != nil {
		log.Fatal("Failed to clear token:", result.Error)
	}

	fmt.Printf("Cleared token for develop cluster. Rows affected: %d\n", result.RowsAffected)
}
