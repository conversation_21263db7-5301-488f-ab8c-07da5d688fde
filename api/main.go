package main

import (
	"flag"
	"fmt"
	"log"

	"github.com/privasea/motivus/config"
	"github.com/privasea/motivus/models"
	"github.com/privasea/motivus/routes"
)

func main() {
	configPath := flag.String("config", "config/config.yaml", "Path to configuration file")
	flag.Parse()

	// Load configuration
	if err := config.LoadConfig(*configPath); err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database
	if err := models.InitDB(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Setup router
	r := routes.SetupRouter()

	// Start server
	serverAddr := fmt.Sprintf(":%d", config.AppConfig.Server.Port)
	log.Printf("Starting server on %s", serverAddr)
	if err := r.Run(serverAddr); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
