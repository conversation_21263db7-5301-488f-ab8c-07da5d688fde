package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/privasea/motivus/config"
	"github.com/privasea/motivus/controllers"
	"github.com/privasea/motivus/middleware"
	"github.com/privasea/motivus/services"
)

// SetupRouter configures the API routes
func SetupRouter() *gin.Engine {
	// Set Gin mode
	gin.SetMode(config.AppConfig.Server.Mode)

	r := gin.Default()

	// Add middleware
	r.Use(middleware.CORS())

	// API v1 routes
	v1 := r.Group("/api/v1")
	{
		// Public routes
		authController := controllers.NewAuthController()
		auth := v1.Group("/auth")
		{
			auth.POST("/login", authController.Login)
			auth.POST("/register", authController.Register)

			// Protected auth routes
			authProtected := auth.Group("/")
			authProtected.Use(middleware.JWTAuth())
			{
				authProtected.GET("/profile", authController.GetProfile)
				authProtected.PUT("/profile", authController.UpdateProfile)
			}
		}

		// Protected routes
		api := v1.Group("/")
		api.Use(middleware.JWTAuth())
		{
			// Project routes
			projectController := controllers.NewProjectController()
			projects := api.Group("/projects")
			{
				projects.POST("", projectController.CreateProject)
				projects.GET("", projectController.ListProjects)
				projects.GET("/:id", projectController.GetProject)
				projects.PUT("/:id", projectController.UpdateProject)
				projects.DELETE("/:id", projectController.DeleteProject)

				// Project tags
				projects.POST("/:id/tags", projectController.AddProjectTag)
				projects.GET("/:id/tags", projectController.GetProjectTags)
				projects.DELETE("/:id/tags/:tagId", projectController.RemoveProjectTag)
			}

			// Release routes
			releaseController := controllers.NewReleaseController()
			releases := api.Group("/releases")
			{
				releases.POST("", releaseController.CreateRelease)
				releases.GET("", releaseController.ListReleases)
				releases.GET("/:id", releaseController.GetRelease)
				releases.PUT("/:id", releaseController.UpdateRelease)
				releases.DELETE("/:id", releaseController.DeleteRelease)

				// Release deployment
				releases.POST("/:id/deploy", releaseController.DeployRelease)
				releases.POST("/:id/rollback/:historyId", releaseController.RollbackRelease)
				releases.GET("/:id/history", releaseController.GetReleaseHistory)
			}

			// Plan routes
			planController := controllers.NewPlanController()
			plans := api.Group("/plans")
			{
				plans.POST("", planController.CreatePlan)
				plans.GET("", planController.ListPlans)
				plans.GET("/:id", planController.GetPlan)
				plans.PUT("/:id", planController.UpdatePlan)
				plans.DELETE("/:id", planController.DeletePlan)

				// Plan execution
				plans.POST("/:id/execute", planController.ExecutePlan)
				plans.POST("/:id/schedule", planController.SchedulePlan)

				// Plan items
				plans.POST("/:id/items", planController.AddPlanItem)
				plans.GET("/:id/items", planController.GetPlanItems)
				plans.PUT("/:id/items/:itemId", planController.UpdatePlanItem)
				plans.DELETE("/:id/items/:itemId", planController.DeletePlanItem)

				// Plan item releases
				plans.POST("/items/:itemId/releases", planController.AddPlanItemRelease)
				plans.GET("/items/:itemId/releases", planController.GetPlanItemReleases)
				plans.PUT("/items/releases/:releaseId", planController.UpdatePlanItemRelease)
				plans.DELETE("/items/releases/:releaseId", planController.DeletePlanItemRelease)
			}

			// Config routes (Secret/ConfigMap)
			configController := controllers.NewConfigController()
			configs := api.Group("/configs")
			{
				// Secret management
				configs.POST("/secrets", configController.CreateSecret)
				configs.GET("/secrets", configController.ListSecrets)
				configs.GET("/secrets/:name", configController.GetSecret)
				configs.PUT("/secrets/:name", configController.UpdateSecret)
				configs.DELETE("/secrets/:name", configController.DeleteSecret)

				// ConfigMap management
				configs.POST("/configmaps", configController.CreateConfigMap)
				configs.GET("/configmaps", configController.ListConfigMaps)
				configs.GET("/configmaps/:name", configController.GetConfigMap)
				configs.PUT("/configmaps/:name", configController.UpdateConfigMap)
				configs.DELETE("/configmaps/:name", configController.DeleteConfigMap)

				// Release values management
				configs.GET("/releases/:id/values", configController.GetReleaseValues)
				configs.PUT("/releases/:id/values", configController.UpdateReleaseValues)
			}

			// Route management (APISIX)
			routeController := controllers.NewRouteController()
			routes := api.Group("/routes")
			{
				// Route management
				routes.POST("", routeController.CreateRoute)
				routes.GET("", routeController.ListRoutes)
				routes.GET("/:id", routeController.GetRoute)
				routes.PUT("/:id", routeController.UpdateRoute)
				routes.DELETE("/:id", routeController.DeleteRoute)

				// Upstream management
				routes.POST("/upstreams", routeController.CreateUpstream)
				routes.GET("/upstreams", routeController.ListUpstreams)
				routes.GET("/upstreams/:id", routeController.GetUpstream)
				routes.PUT("/upstreams/:id", routeController.UpdateUpstream)
				routes.DELETE("/upstreams/:id", routeController.DeleteUpstream)
			}

			// Cluster management
			clusterController := controllers.NewClusterController()
			clusters := api.Group("/clusters")
			{
				clusters.GET("", clusterController.ListClusters)
				clusters.GET("/:name", clusterController.GetCluster)
				clusters.GET("/:name/details", clusterController.GetClusterDetails)
				clusters.POST("", clusterController.AddCluster)
				clusters.PUT("/:name", clusterController.UpdateCluster)
				clusters.DELETE("/:name", clusterController.DeleteCluster)
				clusters.POST("/:name/default", clusterController.SetDefaultCluster)
			}

			// Namespace management
			namespaces := api.Group("/namespaces")
			{
				namespaces.GET("", clusterController.ListNamespaces)
			}

			// Role and permission management
			roleController := controllers.NewRoleController()
			roles := api.Group("/roles")
			{
				// Role management
				roles.GET("", roleController.GetAllRoles)
				roles.GET("/:name/permissions", roleController.GetRolePermissions)

				// User role management
				roles.GET("/users/:username", roleController.GetUserRoles)
				roles.GET("/users/:username/projects/:projectId", roleController.GetUserProjectRoles)
				roles.POST("/assign", roleController.AssignRoleToUser)
				roles.POST("/projects/assign", roleController.AssignRoleToUserForProject)

				// Permission checking
				roles.GET("/permissions/:name/check", roleController.CheckPermission)
				roles.GET("/permissions/:name/projects/:projectId/check", roleController.CheckProjectPermission)
			}
		}
	}

	// 初始化角色和权限
	roleService := services.RoleService{}
	if err := roleService.InitRolesAndPermissions(); err != nil {
		panic("Failed to initialize roles and permissions: " + err.Error())
	}

	return r
}
