package services

import (
	"errors"
	"fmt"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// User represents a user in the system
type User struct {
	ID        uint      `json:"id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	Password  string    `json:"-"` // Password is not exposed in JSON
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// AuthService handles authentication and user management
type AuthService struct {
	// In a real implementation, this would interact with a database
	// For now, we'll use an in-memory map of users
	users map[string]*User
	nextID uint
}

// NewAuthService creates a new AuthService
func NewAuthService() *AuthService {
	// Create a mock user for testing
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("password"), bcrypt.DefaultCost)
	
	return &AuthService{
		users: map[string]*User{
			"admin": {
				ID:        1,
				Username:  "admin",
				Email:     "<EMAIL>",
				Password:  string(hashedPassword),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
		},
		nextID: 2,
	}
}

// Authenticate authenticates a user with username and password
func (as *AuthService) Authenticate(username, password string) (*User, error) {
	user, exists := as.users[username]
	if !exists {
		return nil, errors.New("user not found")
	}
	
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return nil, errors.New("invalid password")
	}
	
	return user, nil
}

// Register registers a new user
func (as *AuthService) Register(username, password, email string) (*User, error) {
	// Check if username already exists
	if _, exists := as.users[username]; exists {
		return nil, errors.New("username already exists")
	}
	
	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}
	
	// Create user
	user := &User{
		ID:        as.nextID,
		Username:  username,
		Email:     email,
		Password:  string(hashedPassword),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	
	as.users[username] = user
	as.nextID++
	
	return user, nil
}

// GetUserByID gets a user by ID
func (as *AuthService) GetUserByID(id uint) (*User, error) {
	for _, user := range as.users {
		if user.ID == id {
			return user, nil
		}
	}
	
	return nil, errors.New("user not found")
}

// UpdateUser updates a user's profile
func (as *AuthService) UpdateUser(id uint, email, password string) (*User, error) {
	var user *User
	
	// Find user by ID
	for _, u := range as.users {
		if u.ID == id {
			user = u
			break
		}
	}
	
	if user == nil {
		return nil, errors.New("user not found")
	}
	
	// Update email if provided
	if email != "" {
		user.Email = email
	}
	
	// Update password if provided
	if password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
		if err != nil {
			return nil, fmt.Errorf("failed to hash password: %w", err)
		}
		user.Password = string(hashedPassword)
	}
	
	user.UpdatedAt = time.Now()
	
	return user, nil
}
