package services

import (
	"context"
	"fmt"
	"io/ioutil"
	"time"

	"github.com/privasea/motivus/models"
	"github.com/privasea/motivus/pkg/argocd"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

// ClusterInfo represents a Kubernetes cluster
type ClusterInfo struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Server      string `json:"server"`
	Description string `json:"description,omitempty"`
	Status      string `json:"status"`
	Version     string `json:"version,omitempty"`
	NodeCount   int    `json:"node_count,omitempty"`
	IsDefault   bool   `json:"is_default"`
	CreateTime  int    `json:"create_time"`
	UpdateTime  int    `json:"update_time"`
	CreatedBy   string `json:"created_by"`
	UpdatedBy   string `json:"updated_by"`
}

// ClusterService handles operations related to Kubernetes clusters
type ClusterService struct {
	argoCDClient *argocd.ArgoCDClient
}

// NewClusterService creates a new ClusterService
func NewClusterService() *ClusterService {
	return &ClusterService{
		argoCDClient: argocd.NewArgoCDClient(),
	}
}

// ListClusters returns a list of all configured clusters
func (cs *ClusterService) ListClusters() ([]ClusterInfo, error) {
	clusters := make([]ClusterInfo, 0)

	// Get clusters from database
	var dbClusters []models.Cluster
	if err := models.DB.Find(&dbClusters).Error; err != nil {
		return nil, fmt.Errorf("failed to query clusters from database: %w", err)
	}

	// Process clusters from database
	for _, dbCluster := range dbClusters {
		clusterInfo := ClusterInfo{
			ID:          dbCluster.ID,
			Name:        dbCluster.Name,
			Server:      dbCluster.Server,
			Status:      "Unknown", // We'll update this later
			IsDefault:   dbCluster.IsDefault,
			Description: dbCluster.Description,
			CreateTime:  dbCluster.CreateTime,
			UpdateTime:  dbCluster.UpdateTime,
			CreatedBy:   dbCluster.CreatedBy,
			UpdatedBy:   dbCluster.UpdatedBy,
		}

		// Try to connect to the cluster to get status
		clientset, err := cs.getClientsetForCluster(dbCluster.Name)
		if err == nil {
			// Successfully connected, update status
			clusterInfo.Status = "Connected"

			// Try to get version info
			version, err := clientset.Discovery().ServerVersion()
			if err == nil {
				clusterInfo.Version = version.String()
			}

			// Try to get node count
			nodes, err := clientset.CoreV1().Nodes().List(context.Background(), metav1.ListOptions{})
			if err == nil {
				clusterInfo.NodeCount = len(nodes.Items)
			}
		} else {
			clusterInfo.Status = "Error: " + err.Error()
		}

		clusters = append(clusters, clusterInfo)
	}

	return clusters, nil
}

// GetCluster returns information about a specific cluster
func (cs *ClusterService) GetCluster(name string) (*ClusterInfo, error) {
	// Check if the cluster is in the database
	var dbCluster models.Cluster
	if err := models.DB.Where("name = ?", name).First(&dbCluster).Error; err != nil {
		return nil, fmt.Errorf("cluster not found: %s", name)
	}

	clusterInfo := &ClusterInfo{
		ID:          dbCluster.ID,
		Name:        dbCluster.Name,
		Server:      dbCluster.Server,
		Status:      "Unknown",
		IsDefault:   dbCluster.IsDefault,
		Description: dbCluster.Description,
		CreateTime:  dbCluster.CreateTime,
		UpdateTime:  dbCluster.UpdateTime,
		CreatedBy:   dbCluster.CreatedBy,
		UpdatedBy:   dbCluster.UpdatedBy,
	}

	// Try to connect to the cluster
	clientset, err := cs.getClientsetForCluster(name)
	if err == nil {
		clusterInfo.Status = "Connected"

		// Try to get version info
		version, err := clientset.Discovery().ServerVersion()
		if err == nil {
			clusterInfo.Version = version.String()
		}

		// Try to get node count
		nodes, err := clientset.CoreV1().Nodes().List(context.Background(), metav1.ListOptions{})
		if err == nil {
			clusterInfo.NodeCount = len(nodes.Items)
		}
	} else {
		clusterInfo.Status = "Error: " + err.Error()
	}

	return clusterInfo, nil
}

// AddCluster adds a new cluster configuration
func (cs *ClusterService) AddCluster(name, server, token, caData string, isDefault bool, username ...string) error {
	// Get username if provided, otherwise use "system"
	user := "system"
	if len(username) > 0 && username[0] != "" {
		user = username[0]
	}
	// Check if cluster already exists in database
	var count int64
	if err := models.DB.Model(&models.Cluster{}).Where("name = ?", name).Count(&count).Error; err != nil {
		return fmt.Errorf("failed to check if cluster exists: %w", err)
	}
	if count > 0 {
		return fmt.Errorf("cluster already exists: %s", name)
	}

	// If this is the default cluster, unset other defaults
	if isDefault {
		if err := models.DB.Model(&models.Cluster{}).Where("is_default = ?", true).Update("is_default", false).Error; err != nil {
			return fmt.Errorf("failed to unset other default clusters: %w", err)
		}
	}

	// Create new cluster in database
	now := int(time.Now().Unix())
	newCluster := models.Cluster{
		BaseModel: models.BaseModel{
			CreateTime: now,
			UpdateTime: now,
			Deleted:    false,
		},
		Name:      name,
		Server:    server,
		Token:     token,
		CAData:    caData,
		Insecure:  false,
		IsDefault: isDefault,
		CreatedBy: user,
		UpdatedBy: user,
	}

	if err := models.DB.Create(&newCluster).Error; err != nil {
		return fmt.Errorf("failed to create cluster in database: %w", err)
	}

	// Register cluster in ArgoCD
	if err := cs.registerClusterInArgoCD(name, server, token, caData, false); err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Warning: Failed to register cluster in ArgoCD: %v\n", err)
	}

	return nil
}

// UpdateCluster updates an existing cluster configuration
func (cs *ClusterService) UpdateCluster(name, server, token, caData string, isDefault bool, username ...string) error {
	// Get username if provided, otherwise use "system"
	user := "system"
	if len(username) > 0 && username[0] != "" {
		user = username[0]
	}
	// Check if cluster exists in database
	var cluster models.Cluster
	if err := models.DB.Where("name = ?", name).First(&cluster).Error; err != nil {
		return fmt.Errorf("cluster not found: %s", name)
	}

	// Update cluster fields
	updates := map[string]interface{}{
		"server":      server,
		"is_default":  isDefault,
		"update_time": int(time.Now().Unix()),
		"updated_by":  user,
	}

	// Only update token and CA data if provided
	if token != "" {
		updates["token"] = token
	}
	if caData != "" {
		updates["ca_data"] = caData
	}

	// If this is the default cluster, unset other defaults
	if isDefault {
		if err := models.DB.Model(&models.Cluster{}).Where("name <> ? AND is_default = ?", name, true).Update("is_default", false).Error; err != nil {
			return fmt.Errorf("failed to unset other default clusters: %w", err)
		}
	}

	// Update the cluster in database
	if err := models.DB.Model(&cluster).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update cluster in database: %w", err)
	}

	// Get the updated token and CA data
	updatedToken := token
	updatedCAData := caData
	if token == "" {
		updatedToken = cluster.Token
	}
	if caData == "" {
		updatedCAData = cluster.CAData
	}

	// Update cluster in ArgoCD
	if err := cs.updateClusterInArgoCD(name, server, updatedToken, updatedCAData, cluster.Insecure); err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Warning: Failed to update cluster in ArgoCD: %v\n", err)
	}

	return nil
}

// DeleteCluster removes a cluster configuration
func (cs *ClusterService) DeleteCluster(name string) error {
	// Check if cluster exists and get its default status
	var cluster models.Cluster
	if err := models.DB.Where("name = ?", name).First(&cluster).Error; err != nil {
		return fmt.Errorf("cluster not found: %s", name)
	}

	wasDefault := cluster.IsDefault
	serverURL := cluster.Server // Save server URL for ArgoCD

	// Delete the cluster from database
	if err := models.DB.Delete(&cluster).Error; err != nil {
		return fmt.Errorf("failed to delete cluster from database: %w", err)
	}

	// If the deleted cluster was the default, set a new default if possible
	if wasDefault {
		var newDefaultCluster models.Cluster
		if err := models.DB.First(&newDefaultCluster).Error; err == nil {
			// Found another cluster, set it as default
			if err := models.DB.Model(&newDefaultCluster).Update("is_default", true).Error; err != nil {
				return fmt.Errorf("failed to set new default cluster: %w", err)
			}
		}
	}

	// Remove cluster from ArgoCD
	if err := cs.removeClusterFromArgoCD(serverURL); err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Warning: Failed to remove cluster from ArgoCD: %v\n", err)
	}

	return nil
}

// GetDefaultCluster returns the name of the default cluster
func (cs *ClusterService) GetDefaultCluster() string {
	// Check in database
	var defaultCluster models.Cluster
	if err := models.DB.Where("is_default = ?", true).First(&defaultCluster).Error; err == nil {
		return defaultCluster.Name
	}

	// If no default cluster found, return empty string
	return ""
}

// SetDefaultCluster sets a cluster as the default
func (cs *ClusterService) SetDefaultCluster(name string) error {
	// Check if the cluster exists in database
	var cluster models.Cluster
	if err := models.DB.Where("name = ?", name).First(&cluster).Error; err != nil {
		return fmt.Errorf("cluster not found: %s", name)
	}

	// Cluster found in database, set it as default

	// First, unset all default clusters
	if err := models.DB.Model(&models.Cluster{}).Where("is_default = ?", true).Update("is_default", false).Error; err != nil {
		return fmt.Errorf("failed to unset default clusters: %w", err)
	}

	// Then set the specified cluster as default
	if err := models.DB.Model(&cluster).Update("is_default", true).Error; err != nil {
		return fmt.Errorf("failed to set cluster as default: %w", err)
	}

	return nil
}

// ListNamespaces lists all namespaces in a cluster
func (cs *ClusterService) ListNamespaces(clusterName string) ([]string, error) {
	// Get clientset for the cluster
	clientset, err := cs.getClientsetForCluster(clusterName)
	if err != nil {
		return nil, fmt.Errorf("failed to get clientset for cluster %s: %w", clusterName, err)
	}

	// List namespaces
	ctx := context.Background()
	namespaceList, err := clientset.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list namespaces from Kubernetes: %w", err)
	}

	// Extract namespace names
	namespaces := make([]string, 0, len(namespaceList.Items))
	for _, ns := range namespaceList.Items {
		namespaces = append(namespaces, ns.Name)
	}

	return namespaces, nil
}

// getClientsetForCluster returns a Kubernetes clientset for the specified cluster
func (cs *ClusterService) getClientsetForCluster(name string) (*kubernetes.Clientset, error) {
	// 不再对k3d-motivus集群进行特殊处理，所有集群配置都从数据库获取

	// Check if the cluster is in the database
	var dbCluster models.Cluster
	if err := models.DB.Where("name = ?", name).First(&dbCluster).Error; err != nil {
		return nil, fmt.Errorf("cluster not found in database: %s", name)
	}

	// Create rest config from database cluster
	k8sConfig := &rest.Config{
		Host: dbCluster.Server,
		TLSClientConfig: rest.TLSClientConfig{
			Insecure: dbCluster.Insecure,
		},
	}

	// Set token if provided
	if dbCluster.Token != "" {
		k8sConfig.BearerToken = dbCluster.Token
	}

	// Set CA data or file
	if dbCluster.CAData != "" {
		k8sConfig.TLSClientConfig.CAData = []byte(dbCluster.CAData)
	} else if dbCluster.CAFile != "" {
		caData, err := ioutil.ReadFile(dbCluster.CAFile)
		if err != nil {
			return nil, fmt.Errorf("failed to read CA file: %w", err)
		}
		k8sConfig.TLSClientConfig.CAData = caData
	}

	return kubernetes.NewForConfig(k8sConfig)
}

// registerClusterInArgoCD registers a cluster in ArgoCD
func (cs *ClusterService) registerClusterInArgoCD(name, server, token, caData string, insecure bool) error {
	// Login to ArgoCD
	if err := cs.argoCDClient.Login(); err != nil {
		return fmt.Errorf("failed to login to ArgoCD: %w", err)
	}

	// Create ArgoCD cluster config
	cluster := &argocd.Cluster{
		Server: server,
		Name:   name,
		Config: argocd.ClusterConfig{
			BearerToken: token,
			TLSClientConfig: argocd.TLSClientConfig{
				Insecure: insecure,
			},
		},
	}

	// Add CA data if provided
	if caData != "" {
		cluster.Config.TLSClientConfig.CAData = caData
		fmt.Printf("Using processed CA data for ArgoCD cluster registration: %s\n", name)
	}

	// Add cluster to ArgoCD
	if err := cs.argoCDClient.AddCluster(cluster); err != nil {
		return fmt.Errorf("failed to register cluster in ArgoCD: %w", err)
	}

	return nil
}

// updateClusterInArgoCD updates a cluster in ArgoCD
func (cs *ClusterService) updateClusterInArgoCD(name, server, token, caData string, insecure bool) error {
	// Login to ArgoCD
	if err := cs.argoCDClient.Login(); err != nil {
		return fmt.Errorf("failed to login to ArgoCD: %w", err)
	}

	// Create ArgoCD cluster config
	cluster := &argocd.Cluster{
		Server: server,
		Name:   name,
		Config: argocd.ClusterConfig{
			TLSClientConfig: argocd.TLSClientConfig{
				Insecure: insecure,
			},
		},
	}

	// Add token if provided
	if token != "" {
		cluster.Config.BearerToken = token
	}

	// Add CA data if provided
	if caData != "" {
		cluster.Config.TLSClientConfig.CAData = caData
		fmt.Printf("Using CA data for ArgoCD cluster update: %s\n", name)
	}

	// Update cluster in ArgoCD
	if err := cs.argoCDClient.UpdateCluster(cluster); err != nil {
		return fmt.Errorf("failed to update cluster in ArgoCD: %w", err)
	}

	return nil
}

// removeClusterFromArgoCD removes a cluster from ArgoCD
func (cs *ClusterService) removeClusterFromArgoCD(server string) error {
	// Login to ArgoCD
	if err := cs.argoCDClient.Login(); err != nil {
		return fmt.Errorf("failed to login to ArgoCD: %w", err)
	}

	// Remove cluster from ArgoCD
	if err := cs.argoCDClient.DeleteCluster(server); err != nil {
		return fmt.Errorf("failed to remove cluster from ArgoCD: %w", err)
	}

	return nil
}

// min returns the smaller of x or y.
func min(x, y int) int {
	if x < y {
		return x
	}
	return y
}
