package services

import (
	"errors"
	"fmt"

	"github.com/privasea/motivus/models"
	"gorm.io/gorm"
)

// ProjectService handles business logic for projects
type ProjectService struct{}

// NewProjectService creates a new ProjectService
func NewProjectService() *ProjectService {
	return &ProjectService{}
}

// CreateProject creates a new project
func (ps *ProjectService) CreateProject(project *models.Project) error {
	return models.DB.Create(project).Error
}

// GetProjectByID gets a project by ID
func (ps *ProjectService) GetProjectByID(id uint) (*models.Project, error) {
	var project models.Project
	result := models.DB.First(&project, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("project not found: %w", result.Error)
		}
		return nil, result.Error
	}
	return &project, nil
}

// UpdateProject updates a project
func (ps *ProjectService) UpdateProject(project *models.Project) error {
	return models.DB.Save(project).Error
}

// DeleteProject soft deletes a project
func (ps *ProjectService) DeleteProject(id uint) error {
	return models.DB.Model(&models.Project{}).Where("id = ?", id).Update("deleted", true).Error
}

// ListProjects lists all non-deleted projects
func (ps *ProjectService) ListProjects() ([]models.Project, error) {
	var projects []models.Project
	result := models.DB.Where("deleted = ?", false).Find(&projects)
	return projects, result.Error
}

// AddProjectTag adds a tag to a project
func (ps *ProjectService) AddProjectTag(tag *models.ProjectTag) error {
	return models.DB.Create(tag).Error
}

// RemoveProjectTag removes a tag from a project
func (ps *ProjectService) RemoveProjectTag(projectID, tagID uint) error {
	return models.DB.Where("project_id = ? AND id = ?", projectID, tagID).Delete(&models.ProjectTag{}).Error
}

// GetProjectTags gets all tags for a project
func (ps *ProjectService) GetProjectTags(projectID uint) ([]models.ProjectTag, error) {
	var tags []models.ProjectTag
	result := models.DB.Where("project_id = ?", projectID).Find(&tags)
	return tags, result.Error
}

// AddUserToProject adds a user to a project with a specific role
func (ps *ProjectService) AddUserToProject(userProject *models.UserProject) error {
	return models.DB.Create(userProject).Error
}

// RemoveUserFromProject removes a user from a project
func (ps *ProjectService) RemoveUserFromProject(userID, projectID uint) error {
	return models.DB.Where("username = ? AND project_id = ?", userID, projectID).Delete(&models.UserProject{}).Error
}

// GetProjectUsers gets all users for a project
func (ps *ProjectService) GetProjectUsers(projectID uint) ([]models.UserProject, error) {
	var users []models.UserProject
	result := models.DB.Where("project_id = ?", projectID).Find(&users)
	return users, result.Error
}
