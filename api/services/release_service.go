package services

import (
	"errors"
	"fmt"
	"time"

	"github.com/privasea/motivus/models"
	"github.com/privasea/motivus/pkg/argocd"
	"github.com/privasea/motivus/pkg/helm"
	"gorm.io/gorm"
)

// ReleaseService handles business logic for releases
type ReleaseService struct {
	helmClient   *helm.ChartMuseumClient
	argoCDClient *argocd.ArgoCDClient
}

// NewReleaseService creates a new ReleaseService
func NewReleaseService() *ReleaseService {
	return &ReleaseService{
		helmClient:   helm.NewChartMuseumClient(),
		argoCDClient: argocd.NewArgoCDClient(),
	}
}

// CreateRelease creates a new release
func (rs *ReleaseService) CreateRelease(release *models.Release) error {
	// Validate project exists
	var project models.Project
	if err := models.DB.First(&project, release.ProjectID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("project not found: %w", err)
		}
		return err
	}

	// Create release
	return models.DB.Create(release).Error
}

// GetReleaseByID gets a release by ID
func (rs *ReleaseService) GetReleaseByID(id uint) (*models.Release, error) {
	var release models.Release
	result := models.DB.First(&release, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("release not found: %w", result.Error)
		}
		return nil, result.Error
	}
	return &release, nil
}

// UpdateRelease updates a release
func (rs *ReleaseService) UpdateRelease(release *models.Release) error {
	return models.DB.Save(release).Error
}

// DeleteRelease soft deletes a release
func (rs *ReleaseService) DeleteRelease(id uint) error {
	return models.DB.Model(&models.Release{}).Where("id = ?", id).Update("deleted", true).Error
}

// ListReleases lists all non-deleted releases
func (rs *ReleaseService) ListReleases() ([]models.Release, error) {
	var releases []models.Release
	result := models.DB.Where("deleted = ?", false).Find(&releases)
	return releases, result.Error
}

// GetReleasesByProjectID gets all releases for a project
func (rs *ReleaseService) GetReleasesByProjectID(projectID uint) ([]models.Release, error) {
	var releases []models.Release
	result := models.DB.Where("project_id = ? AND deleted = ?", projectID, false).Find(&releases)
	return releases, result.Error
}

// DeployRelease deploys a release and creates a release history record
func (rs *ReleaseService) DeployRelease(releaseID uint, chartVersion, imageTag, changeLog, username string) (*models.ReleaseHistory, error) {
	// Get release
	release, err := rs.GetReleaseByID(releaseID)
	if err != nil {
		return nil, err
	}

	// Get project
	var project models.Project
	if err := models.DB.First(&project, release.ProjectID).Error; err != nil {
		return nil, err
	}

	// Get previous release history
	var prevHistory models.ReleaseHistory
	prevHistoryExists := true
	if err := models.DB.Where("release_id = ?", releaseID).Order("id DESC").First(&prevHistory).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			prevHistoryExists = false
		} else {
			return nil, err
		}
	}

	// Create new release history
	history := models.ReleaseHistory{
		ProjectID:        release.ProjectID,
		ReleaseID:        releaseID,
		ReleaseName:      release.ReleaseName,
		ChartVersion:     chartVersion,
		ChartValues:      release.ChartValues,
		ImageTag:         imageTag,
		Username:         username,
		ChangeLog:        changeLog,
		HType:            "deploy",
	}

	if prevHistoryExists {
		history.PrevChartVersion = prevHistory.ChartVersion
		history.PrevChartValues = prevHistory.ChartValues
		history.PrevImageTag = prevHistory.ImageTag
	} else {
		history.PrevChartVersion = ""
		history.PrevChartValues = ""
		history.PrevImageTag = ""
	}

	// Start transaction
	tx := models.DB.Begin()

	// Create history record
	if err := tx.Create(&history).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Update release with new chart version
	release.ChartVersion = chartVersion
	if err := tx.Save(release).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Create action log
	actionLog := models.ActionLog{
		Username:         username,
		ActionCode:       "deploy",
		ProjectID:        release.ProjectID,
		ReleaseID:        fmt.Sprintf("%d", releaseID),
		ReleaseHistoryID: history.ID,
		ProjectName:      project.ProjectName,
		ReleaseName:      release.ReleaseName,
	}

	if err := tx.Create(&actionLog).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// Deploy to ArgoCD (this is done after DB transaction to avoid leaving DB in inconsistent state if deployment fails)
	if err := rs.deployToArgoCD(release, chartVersion, imageTag); err != nil {
		// Log error but don't fail the operation
		// In a real system, you might want to update the history record with the error
		fmt.Printf("Error deploying to ArgoCD: %v\n", err)
	}

	return &history, nil
}

// RollbackRelease rolls back a release to a previous version
func (rs *ReleaseService) RollbackRelease(releaseID, historyID uint, username string) (*models.ReleaseHistory, error) {
	// Get release
	release, err := rs.GetReleaseByID(releaseID)
	if err != nil {
		return nil, err
	}

	// Get project
	var project models.Project
	if err := models.DB.First(&project, release.ProjectID).Error; err != nil {
		return nil, err
	}

	// Get target history
	var targetHistory models.ReleaseHistory
	if err := models.DB.First(&targetHistory, historyID).Error; err != nil {
		return nil, err
	}

	// Verify history belongs to release
	if targetHistory.ReleaseID != releaseID {
		return nil, errors.New("history does not belong to release")
	}

	// Get current release history
	var currentHistory models.ReleaseHistory
	if err := models.DB.Where("release_id = ?", releaseID).Order("id DESC").First(&currentHistory).Error; err != nil {
		return nil, err
	}

	// Create new release history for rollback
	history := models.ReleaseHistory{
		ProjectID:        release.ProjectID,
		ReleaseID:        releaseID,
		ReleaseName:      release.ReleaseName,
		PrevChartVersion: currentHistory.ChartVersion,
		ChartVersion:     targetHistory.ChartVersion,
		PrevChartValues:  currentHistory.ChartValues,
		ChartValues:      targetHistory.ChartValues,
		PrevImageTag:     currentHistory.ImageTag,
		ImageTag:         targetHistory.ImageTag,
		Username:         username,
		ChangeLog:        fmt.Sprintf("Rollback to version %s", targetHistory.ChartVersion),
		HType:            "rollback",
	}

	// Start transaction
	tx := models.DB.Begin()

	// Create history record
	if err := tx.Create(&history).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Update release with rolled back chart version
	release.ChartVersion = targetHistory.ChartVersion
	release.ChartValues = targetHistory.ChartValues
	if err := tx.Save(release).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Create action log
	actionLog := models.ActionLog{
		Username:         username,
		ActionCode:       "rollback",
		ProjectID:        release.ProjectID,
		ReleaseID:        fmt.Sprintf("%d", releaseID),
		ReleaseHistoryID: history.ID,
		ProjectName:      project.ProjectName,
		ReleaseName:      release.ReleaseName,
	}

	if err := tx.Create(&actionLog).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// Deploy to ArgoCD
	if err := rs.deployToArgoCD(release, targetHistory.ChartVersion, targetHistory.ImageTag); err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Error deploying to ArgoCD: %v\n", err)
	}

	return &history, nil
}

// GetReleaseHistory gets the deployment history of a release
func (rs *ReleaseService) GetReleaseHistory(releaseID uint) ([]models.ReleaseHistory, error) {
	var history []models.ReleaseHistory
	result := models.DB.Where("release_id = ?", releaseID).Order("id DESC").Find(&history)
	return history, result.Error
}

// deployToArgoCD deploys a release to ArgoCD
func (rs *ReleaseService) deployToArgoCD(release *models.Release, chartVersion, imageTag string) error {
	// Get project
	var project models.Project
	if err := models.DB.First(&project, release.ProjectID).Error; err != nil {
		return err
	}

	// Login to ArgoCD
	if err := rs.argoCDClient.Login(); err != nil {
		return fmt.Errorf("failed to login to ArgoCD: %w", err)
	}

	// Check if application exists
	app, err := rs.argoCDClient.GetApplication(release.ReleaseName)
	if err != nil {
		// Application doesn't exist, create it
		app = &argocd.Application{
			Metadata: struct {
				Name      string            `json:"name"`
				Namespace string            `json:"namespace"`
				Labels    map[string]string `json:"labels,omitempty"`
			}{
				Name:      release.ReleaseName,
				Namespace: "argocd",
				Labels: map[string]string{
					"app.kubernetes.io/instance": release.ReleaseName,
					"app.kubernetes.io/part-of":  project.ProjectName,
				},
			},
			Spec: struct {
				Project string `json:"project"`
				Source  struct {
					RepoURL        string `json:"repoURL"`
					Path           string `json:"path,omitempty"`
					TargetRevision string `json:"targetRevision"`
					Chart          string `json:"chart,omitempty"`
					Helm           struct {
						Parameters []struct {
							Name  string `json:"name"`
							Value string `json:"value"`
						} `json:"parameters,omitempty"`
						Values string `json:"values,omitempty"`
					} `json:"helm,omitempty"`
				} `json:"source"`
				Destination struct {
					Server    string `json:"server"`
					Namespace string `json:"namespace"`
				} `json:"destination"`
				SyncPolicy struct {
					Automated struct {
						Prune      bool `json:"prune"`
						SelfHeal   bool `json:"selfHeal"`
						AllowEmpty bool `json:"allowEmpty"`
					} `json:"automated,omitempty"`
					SyncOptions []string `json:"syncOptions,omitempty"`
				} `json:"syncPolicy,omitempty"`
			}{
				Project: "default",
				Source: struct {
					RepoURL        string `json:"repoURL"`
					Path           string `json:"path,omitempty"`
					TargetRevision string `json:"targetRevision"`
					Chart          string `json:"chart,omitempty"`
					Helm           struct {
						Parameters []struct {
							Name  string `json:"name"`
							Value string `json:"value"`
						} `json:"parameters,omitempty"`
						Values string `json:"values,omitempty"`
					} `json:"helm,omitempty"`
				}{
					RepoURL:        project.ChartRepoURL,
					TargetRevision: chartVersion,
					Chart:          project.ChartName,
					Helm: struct {
						Parameters []struct {
							Name  string `json:"name"`
							Value string `json:"value"`
						} `json:"parameters,omitempty"`
						Values string `json:"values,omitempty"`
					}{
						Parameters: []struct {
							Name  string `json:"name"`
							Value string `json:"value"`
						}{
							{
								Name:  "image.tag",
								Value: imageTag,
							},
						},
						Values: release.ChartValues,
					},
				},
				Destination: struct {
					Server    string `json:"server"`
					Namespace string `json:"namespace"`
				}{
					Server:    "https://kubernetes.default.svc",
					Namespace: release.ClusterNamespace,
				},
				SyncPolicy: struct {
					Automated struct {
						Prune      bool `json:"prune"`
						SelfHeal   bool `json:"selfHeal"`
						AllowEmpty bool `json:"allowEmpty"`
					} `json:"automated,omitempty"`
					SyncOptions []string `json:"syncOptions,omitempty"`
				}{
					Automated: struct {
						Prune      bool `json:"prune"`
						SelfHeal   bool `json:"selfHeal"`
						AllowEmpty bool `json:"allowEmpty"`
					}{
						Prune:    true,
						SelfHeal: true,
					},
					SyncOptions: []string{"CreateNamespace=true"},
				},
			},
		}

		if err := rs.argoCDClient.CreateApplication(app); err != nil {
			return fmt.Errorf("failed to create application in ArgoCD: %w", err)
		}
	} else {
		// Application exists, update it
		app.Spec.Source.TargetRevision = chartVersion
		app.Spec.Source.Helm.Values = release.ChartValues
		
		// Update image tag parameter
		found := false
		for i, param := range app.Spec.Source.Helm.Parameters {
			if param.Name == "image.tag" {
				app.Spec.Source.Helm.Parameters[i].Value = imageTag
				found = true
				break
			}
		}
		
		if !found {
			app.Spec.Source.Helm.Parameters = append(app.Spec.Source.Helm.Parameters, struct {
				Name  string `json:"name"`
				Value string `json:"value"`
			}{
				Name:  "image.tag",
				Value: imageTag,
			})
		}

		if err := rs.argoCDClient.UpdateApplication(app); err != nil {
			return fmt.Errorf("failed to update application in ArgoCD: %w", err)
		}
	}

	// Sync application
	if err := rs.argoCDClient.SyncApplication(release.ReleaseName); err != nil {
		return fmt.Errorf("failed to sync application in ArgoCD: %w", err)
	}

	// Wait for sync to complete (with timeout)
	if err := rs.argoCDClient.WaitForSync(release.ReleaseName, 5*time.Minute); err != nil {
		return fmt.Errorf("failed to wait for application sync: %w", err)
	}

	return nil
}
