package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/privasea/motivus/models"
	"github.com/privasea/motivus/pkg/k8s"
	"gorm.io/gorm"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// ConfigService handles business logic for configuration management
type ConfigService struct {
	k8sClient *k8s.K8sClient
	cluster   string
}

// Secret represents a Kubernetes Secret
type Secret struct {
	Name       string            `json:"name"`
	Namespace  string            `json:"namespace"`
	Data       map[string]string `json:"data"`
	Labels     map[string]string `json:"labels,omitempty"`
	CreateTime int               `json:"create_time"`
	UpdateTime int               `json:"update_time"`
	Creator    string            `json:"creator"`
	Updater    string            `json:"updater"`
}

// ConfigMap represents a Kubernetes ConfigMap
type ConfigMap struct {
	Name       string            `json:"name"`
	Namespace  string            `json:"namespace"`
	Data       map[string]string `json:"data"`
	Labels     map[string]string `json:"labels,omitempty"`
	CreateTime int               `json:"create_time"`
	UpdateTime int               `json:"update_time"`
	Creator    string            `json:"creator"`
	Updater    string            `json:"updater"`
}

// NewConfigService creates a new ConfigService for the default cluster
func NewConfigService() *ConfigService {
	return NewConfigServiceForCluster("")
}

// NewConfigServiceForCluster creates a new ConfigService for a specific cluster
func NewConfigServiceForCluster(clusterName string) *ConfigService {
	fmt.Printf("Creating ConfigService for cluster: %s\n", clusterName)

	k8sClient, err := k8s.NewK8sClientForCluster(clusterName)
	if err != nil {
		// Log error but continue with nil client
		// In a real implementation, you might want to handle this differently
		fmt.Printf("Failed to create Kubernetes client for cluster %s: %v\n", clusterName, err)

		// For development/testing, we'll return a service with nil client
		// This will use mock data instead of real Kubernetes API calls
		fmt.Printf("Returning ConfigService with nil client for cluster: %s\n", clusterName)
		return &ConfigService{
			k8sClient: nil,
			cluster:   clusterName,
		}
	}

	fmt.Printf("Successfully created ConfigService for cluster: %s\n", clusterName)
	return &ConfigService{
		k8sClient: k8sClient,
		cluster:   clusterName,
	}
}

// CreateSecret creates a new Secret
func (cs *ConfigService) CreateSecret(name, namespace string, data, labels map[string]string, username string) (*Secret, error) {
	fmt.Printf("CreateSecret called for cluster: %s, namespace: %s, name: %s\n", cs.cluster, namespace, name)

	if cs.k8sClient == nil {
		// Return mock data for development/testing when Kubernetes client is not available
		fmt.Println("Warning: Kubernetes client not initialized, returning mock data for CreateSecret")
		now := time.Now()
		unixTime := int(now.Unix())

		// Create a mock Secret
		return &Secret{
			Name:       name,
			Namespace:  namespace,
			Data:       data,
			Labels:     labels,
			CreateTime: unixTime,
			UpdateTime: unixTime,
			Creator:    username,
			Updater:    username,
		}, nil
	}

	fmt.Printf("Using K8sClient for cluster: %s\n", cs.k8sClient.GetCluster())

	// Create metadata
	now := time.Now()

	// Add creator/updater to labels
	if labels == nil {
		labels = make(map[string]string)
	}
	labels["creator"] = username
	labels["updater"] = username
	// 使用符合Kubernetes标签值要求的时间格式（不含冒号和其他特殊字符）
	labels["create_time"] = now.Format("2006-01-02T150405Z")
	labels["update_time"] = now.Format("2006-01-02T150405Z")

	// Create Secret object
	// 前端传来的是原始数据，直接使用StringData字段
	// Kubernetes会自动将StringData转换为Data并进行base64编码
	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
			Labels:    labels,
		},
		StringData: data,
		Type:       corev1.SecretTypeOpaque,
	}

	// Create Secret in Kubernetes
	ctx := context.Background()
	createdSecret, err := cs.k8sClient.CreateSecret(ctx, secret)
	if err != nil {
		return nil, fmt.Errorf("failed to create Secret in Kubernetes: %w", err)
	}

	// Convert to our Secret type
	return cs.convertK8sSecret(createdSecret, username, username), nil
}

// GetSecret gets a Secret by name and namespace
func (cs *ConfigService) GetSecret(name, namespace string) (*Secret, error) {
	if cs.k8sClient == nil {
		// Return mock data for development/testing when Kubernetes client is not available
		fmt.Println("Warning: Kubernetes client not initialized, returning mock data for GetSecret")
		now := time.Now()
		unixTime := int(now.Unix())

		// Create a mock Secret
		return &Secret{
			Name:       name,
			Namespace:  namespace,
			Data:       map[string]string{"username": "YWRtaW4=", "password": "cGFzc3dvcmQxMjM="},
			Labels:     map[string]string{"app": "example"},
			CreateTime: unixTime,
			UpdateTime: unixTime,
			Creator:    "system",
			Updater:    "system",
		}, nil
	}

	// Get Secret from Kubernetes
	ctx := context.Background()
	secret, err := cs.k8sClient.GetSecret(ctx, name, namespace)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return nil, fmt.Errorf("secret not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get Secret from Kubernetes: %w", err)
	}

	// Get creator and updater from labels
	creator := secret.Labels["creator"]
	if creator == "" {
		creator = "unknown"
	}

	updater := secret.Labels["updater"]
	if updater == "" {
		updater = "unknown"
	}

	// Convert to our Secret type
	return cs.convertK8sSecret(secret, creator, updater), nil
}

// UpdateSecret updates a Secret
func (cs *ConfigService) UpdateSecret(name, namespace string, data, labels map[string]string, username string) (*Secret, error) {
	if cs.k8sClient == nil {
		// Return mock data for development/testing when Kubernetes client is not available
		fmt.Println("Warning: Kubernetes client not initialized, returning mock data for UpdateSecret")
		now := time.Now()
		unixTime := int(now.Unix())

		// Create a mock Secret
		return &Secret{
			Name:       name,
			Namespace:  namespace,
			Data:       data,
			Labels:     labels,
			CreateTime: unixTime,
			UpdateTime: unixTime,
			Creator:    username,
			Updater:    username,
		}, nil
	}

	// Get existing Secret
	ctx := context.Background()
	existingSecret, err := cs.k8sClient.GetSecret(ctx, name, namespace)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return nil, fmt.Errorf("secret not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get Secret from Kubernetes: %w", err)
	}

	// Update metadata
	now := time.Now()

	// Get creator from existing labels
	creator := existingSecret.Labels["creator"]
	if creator == "" {
		creator = "unknown"
	}

	// Update labels
	if labels == nil {
		labels = make(map[string]string)
	}
	for k, v := range existingSecret.Labels {
		if _, exists := labels[k]; !exists {
			labels[k] = v
		}
	}
	labels["updater"] = username
	// 使用符合Kubernetes标签值要求的时间格式（不含冒号和其他特殊字符）
	labels["update_time"] = now.Format("2006-01-02T150405Z")

	// Create updated Secret object
	// 前端传来的是原始数据，直接使用StringData字段
	// Kubernetes会自动将StringData转换为Data并进行base64编码
	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
			Labels:    labels,
		},
		StringData: data,
		Type:       corev1.SecretTypeOpaque,
	}

	// Update Secret in Kubernetes
	updatedSecret, err := cs.k8sClient.UpdateSecret(ctx, secret)
	if err != nil {
		return nil, fmt.Errorf("failed to update Secret in Kubernetes: %w", err)
	}

	// Convert to our Secret type
	return cs.convertK8sSecret(updatedSecret, creator, username), nil
}

// DeleteSecret deletes a Secret
func (cs *ConfigService) DeleteSecret(name, namespace, username string) error {
	if cs.k8sClient == nil {
		// Return success for development/testing when Kubernetes client is not available
		fmt.Println("Warning: Kubernetes client not initialized, mocking DeleteSecret")
		return nil
	}

	// Delete Secret from Kubernetes
	ctx := context.Background()
	err := cs.k8sClient.DeleteSecret(ctx, name, namespace)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return fmt.Errorf("secret not found: %w", err)
		}
		return fmt.Errorf("failed to delete Secret from Kubernetes: %w", err)
	}

	return nil
}

// ListSecrets lists all Secrets in a namespace
func (cs *ConfigService) ListSecrets(namespace string) ([]Secret, error) {
	if cs.k8sClient == nil {
		// Return mock data for development/testing when Kubernetes client is not available
		fmt.Println("Warning: Kubernetes client not initialized, returning mock data for Secrets")
		now := time.Now().Unix()
		return []Secret{
			{
				Name:       "example-secret",
				Namespace:  namespace,
				Data:       map[string]string{"username": "YWRtaW4=", "password": "cGFzc3dvcmQxMjM="},
				Labels:     map[string]string{"app": "example"},
				CreateTime: int(now),
				UpdateTime: int(now),
				Creator:    "system",
				Updater:    "system",
			},
		}, nil
	}

	// List Secrets from Kubernetes
	ctx := context.Background()
	secretList, err := cs.k8sClient.ListSecrets(ctx, namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to list Secrets from Kubernetes: %w", err)
	}

	// Convert to our Secret type
	secrets := make([]Secret, 0, len(secretList.Items))
	for _, secret := range secretList.Items {
		// Skip service account secrets and other system secrets
		if secret.Type == corev1.SecretTypeServiceAccountToken ||
			secret.Type == corev1.SecretTypeDockercfg ||
			secret.Type == corev1.SecretTypeDockerConfigJson {
			continue
		}

		// Get creator and updater from labels
		creator := secret.Labels["creator"]
		if creator == "" {
			creator = "unknown"
		}

		updater := secret.Labels["updater"]
		if updater == "" {
			updater = "unknown"
		}

		secrets = append(secrets, *cs.convertK8sSecret(&secret, creator, updater))
	}

	return secrets, nil
}

// CreateConfigMap creates a new ConfigMap
func (cs *ConfigService) CreateConfigMap(name, namespace string, data, labels map[string]string, username string) (*ConfigMap, error) {
	if cs.k8sClient == nil {
		// Return mock data for development/testing when Kubernetes client is not available
		fmt.Println("Warning: Kubernetes client not initialized, returning mock data for CreateConfigMap")
		now := time.Now()
		unixTime := int(now.Unix())

		// Create a mock ConfigMap
		return &ConfigMap{
			Name:       name,
			Namespace:  namespace,
			Data:       data,
			Labels:     labels,
			CreateTime: unixTime,
			UpdateTime: unixTime,
			Creator:    username,
			Updater:    username,
		}, nil
	}

	// Create metadata
	now := time.Now()

	// Add creator/updater to labels
	if labels == nil {
		labels = make(map[string]string)
	}
	labels["creator"] = username
	labels["updater"] = username
	// 使用符合Kubernetes标签值要求的时间格式（不含冒号和其他特殊字符）
	labels["create_time"] = now.Format("2006-01-02T150405Z")
	labels["update_time"] = now.Format("2006-01-02T150405Z")

	// Create ConfigMap object
	configMap := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
			Labels:    labels,
		},
		Data: data,
	}

	// Create ConfigMap in Kubernetes
	ctx := context.Background()
	createdConfigMap, err := cs.k8sClient.CreateConfigMap(ctx, configMap)
	if err != nil {
		return nil, fmt.Errorf("failed to create ConfigMap in Kubernetes: %w", err)
	}

	// Convert to our ConfigMap type
	return cs.convertK8sConfigMap(createdConfigMap, username, username), nil
}

// GetConfigMap gets a ConfigMap by name and namespace
func (cs *ConfigService) GetConfigMap(name, namespace string) (*ConfigMap, error) {
	if cs.k8sClient == nil {
		// Return mock data for development/testing when Kubernetes client is not available
		fmt.Println("Warning: Kubernetes client not initialized, returning mock data for GetConfigMap")
		now := time.Now()
		unixTime := int(now.Unix())

		// Create a mock ConfigMap
		return &ConfigMap{
			Name:       name,
			Namespace:  namespace,
			Data:       map[string]string{"config.json": "{\"key\": \"value\"}", "settings.yaml": "key: value"},
			Labels:     map[string]string{"app": "example"},
			CreateTime: unixTime,
			UpdateTime: unixTime,
			Creator:    "system",
			Updater:    "system",
		}, nil
	}

	// Get ConfigMap from Kubernetes
	ctx := context.Background()
	configMap, err := cs.k8sClient.GetConfigMap(ctx, name, namespace)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return nil, fmt.Errorf("configmap not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get ConfigMap from Kubernetes: %w", err)
	}

	// Get creator and updater from labels
	creator := configMap.Labels["creator"]
	if creator == "" {
		creator = "unknown"
	}

	updater := configMap.Labels["updater"]
	if updater == "" {
		updater = "unknown"
	}

	// Convert to our ConfigMap type
	return cs.convertK8sConfigMap(configMap, creator, updater), nil
}

// UpdateConfigMap updates a ConfigMap
func (cs *ConfigService) UpdateConfigMap(name, namespace string, data, labels map[string]string, username string) (*ConfigMap, error) {
	if cs.k8sClient == nil {
		// Return mock data for development/testing when Kubernetes client is not available
		fmt.Println("Warning: Kubernetes client not initialized, returning mock data for UpdateConfigMap")
		now := time.Now()
		unixTime := int(now.Unix())

		// Create a mock ConfigMap
		return &ConfigMap{
			Name:       name,
			Namespace:  namespace,
			Data:       data,
			Labels:     labels,
			CreateTime: unixTime,
			UpdateTime: unixTime,
			Creator:    username,
			Updater:    username,
		}, nil
	}

	// Get existing ConfigMap
	ctx := context.Background()
	existingConfigMap, err := cs.k8sClient.GetConfigMap(ctx, name, namespace)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return nil, fmt.Errorf("configmap not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get ConfigMap from Kubernetes: %w", err)
	}

	// Update metadata
	now := time.Now()

	// Get creator from existing labels
	creator := existingConfigMap.Labels["creator"]
	if creator == "" {
		creator = "unknown"
	}

	// Update labels
	if labels == nil {
		labels = make(map[string]string)
	}
	for k, v := range existingConfigMap.Labels {
		if _, exists := labels[k]; !exists {
			labels[k] = v
		}
	}
	labels["updater"] = username
	// 使用符合Kubernetes标签值要求的时间格式（不含冒号和其他特殊字符）
	labels["update_time"] = now.Format("2006-01-02T150405Z")

	// Create updated ConfigMap object
	configMap := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
			Labels:    labels,
		},
		Data: data,
	}

	// Update ConfigMap in Kubernetes
	updatedConfigMap, err := cs.k8sClient.UpdateConfigMap(ctx, configMap)
	if err != nil {
		return nil, fmt.Errorf("failed to update ConfigMap in Kubernetes: %w", err)
	}

	// Convert to our ConfigMap type
	return cs.convertK8sConfigMap(updatedConfigMap, creator, username), nil
}

// DeleteConfigMap deletes a ConfigMap
func (cs *ConfigService) DeleteConfigMap(name, namespace, username string) error {
	if cs.k8sClient == nil {
		// Return success for development/testing when Kubernetes client is not available
		fmt.Println("Warning: Kubernetes client not initialized, mocking DeleteConfigMap")
		return nil
	}

	// Delete ConfigMap from Kubernetes
	ctx := context.Background()
	err := cs.k8sClient.DeleteConfigMap(ctx, name, namespace)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return fmt.Errorf("configmap not found: %w", err)
		}
		return fmt.Errorf("failed to delete ConfigMap from Kubernetes: %w", err)
	}

	return nil
}

// ListConfigMaps lists all ConfigMaps in a namespace
func (cs *ConfigService) ListConfigMaps(namespace string) ([]ConfigMap, error) {
	if cs.k8sClient == nil {
		// Return mock data for development/testing when Kubernetes client is not available
		fmt.Println("Warning: Kubernetes client not initialized, returning mock data for ConfigMaps")
		now := time.Now().Unix()
		return []ConfigMap{
			{
				Name:       "example-configmap",
				Namespace:  namespace,
				Data:       map[string]string{"config.json": "{\"key\": \"value\"}", "settings.yaml": "key: value"},
				Labels:     map[string]string{"app": "example"},
				CreateTime: int(now),
				UpdateTime: int(now),
				Creator:    "system",
				Updater:    "system",
			},
		}, nil
	}

	// List ConfigMaps from Kubernetes
	ctx := context.Background()
	configMapList, err := cs.k8sClient.ListConfigMaps(ctx, namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to list ConfigMaps from Kubernetes: %w", err)
	}

	// Convert to our ConfigMap type
	configMaps := make([]ConfigMap, 0, len(configMapList.Items))
	for _, configMap := range configMapList.Items {
		// Skip system ConfigMaps
		if configMap.Namespace == "kube-system" {
			continue
		}

		// Get creator and updater from labels
		creator := configMap.Labels["creator"]
		if creator == "" {
			creator = "unknown"
		}

		updater := configMap.Labels["updater"]
		if updater == "" {
			updater = "unknown"
		}

		configMaps = append(configMaps, *cs.convertK8sConfigMap(&configMap, creator, updater))
	}

	return configMaps, nil
}

// GetReleaseValues gets the values for a release
func (cs *ConfigService) GetReleaseValues(releaseID uint) (string, error) {
	var release models.Release
	if err := models.DB.First(&release, releaseID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", fmt.Errorf("release not found: %w", err)
		}
		return "", err
	}

	return release.ChartValues, nil
}

// UpdateReleaseValues updates the values for a release
func (cs *ConfigService) UpdateReleaseValues(releaseID uint, values, username string) error {
	var release models.Release
	if err := models.DB.First(&release, releaseID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("release not found: %w", err)
		}
		return err
	}

	// Start transaction
	tx := models.DB.Begin()

	// Update release values
	release.ChartValues = values
	if err := tx.Save(&release).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Create action log
	actionLog := models.ActionLog{
		Username:    username,
		ActionCode:  "update_values",
		ProjectID:   release.ProjectID,
		ReleaseID:   fmt.Sprintf("%d", release.ID),
		CreateTime:  int(time.Now().Unix()),
		ProjectName: "", // Would need to fetch project name
		ReleaseName: release.ReleaseName,
	}

	if err := tx.Create(&actionLog).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// Helper functions

// convertK8sSecret converts a Kubernetes Secret to our Secret type
func (cs *ConfigService) convertK8sSecret(secret *corev1.Secret, creator, updater string) *Secret {
	// Convert binary data to string (decoded, not base64 encoded)
	data := make(map[string]string)
	for k, v := range secret.Data {
		// 直接使用解码后的数据，而不是再次进行base64编码
		data[k] = string(v)
	}

	// Parse creation and update times
	createTime := int(secret.CreationTimestamp.Time.Unix())
	updateTime := createTime

	if createTimeStr, ok := secret.Labels["create_time"]; ok {
		if t, err := time.Parse("2006-01-02T150405Z", createTimeStr); err == nil {
			createTime = int(t.Unix())
		}
	}

	if updateTimeStr, ok := secret.Labels["update_time"]; ok {
		if t, err := time.Parse("2006-01-02T150405Z", updateTimeStr); err == nil {
			updateTime = int(t.Unix())
		}
	}

	return &Secret{
		Name:       secret.Name,
		Namespace:  secret.Namespace,
		Data:       data,
		Labels:     secret.Labels,
		CreateTime: createTime,
		UpdateTime: updateTime,
		Creator:    creator,
		Updater:    updater,
	}
}

// convertK8sConfigMap converts a Kubernetes ConfigMap to our ConfigMap type
func (cs *ConfigService) convertK8sConfigMap(configMap *corev1.ConfigMap, creator, updater string) *ConfigMap {
	// Parse creation and update times
	createTime := int(configMap.CreationTimestamp.Time.Unix())
	updateTime := createTime

	if createTimeStr, ok := configMap.Labels["create_time"]; ok {
		if t, err := time.Parse("2006-01-02T150405Z", createTimeStr); err == nil {
			createTime = int(t.Unix())
		}
	}

	if updateTimeStr, ok := configMap.Labels["update_time"]; ok {
		if t, err := time.Parse("2006-01-02T150405Z", updateTimeStr); err == nil {
			updateTime = int(t.Unix())
		}
	}

	return &ConfigMap{
		Name:       configMap.Name,
		Namespace:  configMap.Namespace,
		Data:       configMap.Data,
		Labels:     configMap.Labels,
		CreateTime: createTime,
		UpdateTime: updateTime,
		Creator:    creator,
		Updater:    updater,
	}
}
