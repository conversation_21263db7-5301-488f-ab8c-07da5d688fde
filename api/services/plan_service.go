package services

import (
	"errors"
	"fmt"
	"time"

	"github.com/privasea/motivus/models"
	"gorm.io/gorm"
)

// PlanService handles business logic for release plans
type PlanService struct {
	releaseService *ReleaseService
}

// NewPlanService creates a new PlanService
func NewPlanService() *PlanService {
	return &PlanService{
		releaseService: NewReleaseService(),
	}
}

// CreatePlan creates a new release plan
func (ps *PlanService) CreatePlan(plan *models.ReleasePlan) error {
	// Set initial status
	plan.PlanStatus = "created"
	return models.DB.Create(plan).Error
}

// GetPlanByID gets a plan by ID
func (ps *PlanService) GetPlanByID(id uint) (*models.ReleasePlan, error) {
	var plan models.ReleasePlan
	result := models.DB.First(&plan, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("plan not found: %w", result.Error)
		}
		return nil, result.Error
	}
	return &plan, nil
}

// UpdatePlan updates a plan
func (ps *PlanService) UpdatePlan(plan *models.ReleasePlan) error {
	// Get existing plan to check status
	existingPlan, err := ps.GetPlanByID(plan.ID)
	if err != nil {
		return err
	}

	// Only allow updates if plan is not in progress or completed
	if existingPlan.PlanStatus == "in_progress" || existingPlan.PlanStatus == "completed" {
		return errors.New("cannot update plan that is in progress or completed")
	}

	return models.DB.Save(plan).Error
}

// DeletePlan deletes a plan
func (ps *PlanService) DeletePlan(id uint) error {
	// Get existing plan to check status
	existingPlan, err := ps.GetPlanByID(id)
	if err != nil {
		return err
	}

	// Only allow deletion if plan is not in progress
	if existingPlan.PlanStatus == "in_progress" {
		return errors.New("cannot delete plan that is in progress")
	}

	// Start transaction
	tx := models.DB.Begin()

	// Delete plan items first (soft delete)
	if err := tx.Model(&models.PlanItem{}).Where("plan_id = ?", id).Update("deleted", true).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Delete plan
	if err := tx.Delete(&models.ReleasePlan{}, id).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// ListPlans lists all plans
func (ps *PlanService) ListPlans() ([]models.ReleasePlan, error) {
	var plans []models.ReleasePlan
	result := models.DB.Find(&plans)
	return plans, result.Error
}

// AddPlanItem adds an item to a plan
func (ps *PlanService) AddPlanItem(item *models.PlanItem) error {
	// Validate plan exists
	plan, err := ps.GetPlanByID(item.PlanID)
	if err != nil {
		return err
	}

	// Only allow adding items if plan is not in progress or completed
	if plan.PlanStatus == "in_progress" || plan.PlanStatus == "completed" {
		return errors.New("cannot add items to plan that is in progress or completed")
	}

	// Set initial status
	item.ItemStatus = "pending"

	return models.DB.Create(item).Error
}

// UpdatePlanItem updates a plan item
func (ps *PlanService) UpdatePlanItem(item *models.PlanItem) error {
	// Get existing item
	var existingItem models.PlanItem
	if err := models.DB.First(&existingItem, item.ID).Error; err != nil {
		return err
	}

	// Get plan
	plan, err := ps.GetPlanByID(existingItem.PlanID)
	if err != nil {
		return err
	}

	// Only allow updates if plan is not in progress or completed
	if plan.PlanStatus == "in_progress" || plan.PlanStatus == "completed" {
		return errors.New("cannot update items in plan that is in progress or completed")
	}

	return models.DB.Save(item).Error
}

// DeletePlanItem soft deletes a plan item
func (ps *PlanService) DeletePlanItem(id uint) error {
	// Get existing item
	var existingItem models.PlanItem
	if err := models.DB.First(&existingItem, id).Error; err != nil {
		return err
	}

	// Get plan
	plan, err := ps.GetPlanByID(existingItem.PlanID)
	if err != nil {
		return err
	}

	// Only allow deletion if plan is not in progress or completed
	if plan.PlanStatus == "in_progress" || plan.PlanStatus == "completed" {
		return errors.New("cannot delete items from plan that is in progress or completed")
	}

	// Start transaction
	tx := models.DB.Begin()

	// Delete item releases first
	if err := tx.Where("plan_item_id = ?", id).Delete(&models.PlanItemRelease{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Soft delete item
	if err := tx.Model(&models.PlanItem{}).Where("id = ?", id).Update("deleted", true).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// GetPlanItems gets all items for a plan
func (ps *PlanService) GetPlanItems(planID uint) ([]models.PlanItem, error) {
	var items []models.PlanItem
	result := models.DB.Where("plan_id = ? AND deleted = ?", planID, false).Order("item_seq").Find(&items)
	return items, result.Error
}

// AddPlanItemRelease adds a release to a plan item
func (ps *PlanService) AddPlanItemRelease(itemRelease *models.PlanItemRelease) error {
	// Validate item exists
	var item models.PlanItem
	if err := models.DB.First(&item, itemRelease.PlanItemID).Error; err != nil {
		return err
	}

	// Get plan
	plan, err := ps.GetPlanByID(item.PlanID)
	if err != nil {
		return err
	}

	// Only allow adding releases if plan is not in progress or completed
	if plan.PlanStatus == "in_progress" || plan.PlanStatus == "completed" {
		return errors.New("cannot add releases to plan that is in progress or completed")
	}

	// Validate release exists
	var release models.Release
	if err := models.DB.First(&release, itemRelease.ReleaseID).Error; err != nil {
		return err
	}

	// Set initial status
	itemRelease.ReleaseStatus = "pending"

	return models.DB.Create(itemRelease).Error
}

// UpdatePlanItemRelease updates a plan item release
func (ps *PlanService) UpdatePlanItemRelease(itemRelease *models.PlanItemRelease) error {
	// Get existing item release
	var existingRelease models.PlanItemRelease
	if err := models.DB.First(&existingRelease, itemRelease.ID).Error; err != nil {
		return err
	}

	// Get item
	var item models.PlanItem
	if err := models.DB.First(&item, existingRelease.PlanItemID).Error; err != nil {
		return err
	}

	// Get plan
	plan, err := ps.GetPlanByID(item.PlanID)
	if err != nil {
		return err
	}

	// Only allow updates if plan is not in progress or completed
	if plan.PlanStatus == "in_progress" || plan.PlanStatus == "completed" {
		return errors.New("cannot update releases in plan that is in progress or completed")
	}

	return models.DB.Save(itemRelease).Error
}

// DeletePlanItemRelease deletes a plan item release
func (ps *PlanService) DeletePlanItemRelease(id uint) error {
	// Get existing item release
	var existingRelease models.PlanItemRelease
	if err := models.DB.First(&existingRelease, id).Error; err != nil {
		return err
	}

	// Get item
	var item models.PlanItem
	if err := models.DB.First(&item, existingRelease.PlanItemID).Error; err != nil {
		return err
	}

	// Get plan
	plan, err := ps.GetPlanByID(item.PlanID)
	if err != nil {
		return err
	}

	// Only allow deletion if plan is not in progress or completed
	if plan.PlanStatus == "in_progress" || plan.PlanStatus == "completed" {
		return errors.New("cannot delete releases from plan that is in progress or completed")
	}

	return models.DB.Delete(&models.PlanItemRelease{}, id).Error
}

// GetPlanItemReleases gets all releases for a plan item
func (ps *PlanService) GetPlanItemReleases(itemID uint) ([]models.PlanItemRelease, error) {
	var releases []models.PlanItemRelease
	result := models.DB.Where("plan_item_id = ?", itemID).Find(&releases)
	return releases, result.Error
}

// ExecutePlan executes a release plan
func (ps *PlanService) ExecutePlan(planID uint, username string) error {
	// Get plan
	plan, err := ps.GetPlanByID(planID)
	if err != nil {
		return err
	}

	// Check if plan can be executed
	if plan.PlanStatus == "in_progress" {
		return errors.New("plan is already in progress")
	}
	if plan.PlanStatus == "completed" {
		return errors.New("plan is already completed")
	}

	// Update plan status to in_progress
	plan.PlanStatus = "in_progress"
	plan.Updater = username
	if err := models.DB.Save(plan).Error; err != nil {
		return err
	}

	// Get plan items ordered by sequence
	items, err := ps.GetPlanItems(planID)
	if err != nil {
		return err
	}

	// Execute plan items in sequence (in a goroutine)
	go ps.executePlanItems(plan, items, username)

	return nil
}

// executePlanItems executes plan items in sequence
func (ps *PlanService) executePlanItems(plan *models.ReleasePlan, items []models.PlanItem, username string) {
	for _, item := range items {
		// Update item status to in_progress
		item.ItemStatus = "in_progress"
		item.Updater = username
		models.DB.Save(&item)

		// Get item releases
		releases, err := ps.GetPlanItemReleases(item.ID)
		if err != nil {
			// Log error and continue with next item
			fmt.Printf("Error getting releases for item %d: %v\n", item.ID, err)
			item.ItemStatus = "failed"
			models.DB.Save(&item)
			continue
		}

		// Execute releases
		allSuccessful := true
		for _, itemRelease := range releases {
			// Update release status to in_progress
			itemRelease.ReleaseStatus = "in_progress"
			models.DB.Save(&itemRelease)

			// Deploy release
			_, err := ps.releaseService.DeployRelease(
				itemRelease.ReleaseID,
				itemRelease.TargetTag, // Using target tag as chart version for simplicity
				itemRelease.TargetTag,
				itemRelease.ChangeLog,
				username,
			)

			if err != nil {
				// Log error and mark release as failed
				fmt.Printf("Error deploying release %d: %v\n", itemRelease.ReleaseID, err)
				itemRelease.ReleaseStatus = "failed"
				itemRelease.ErrMsg = err.Error()
				models.DB.Save(&itemRelease)
				allSuccessful = false
			} else {
				// Mark release as successful
				itemRelease.ReleaseStatus = "success"
				models.DB.Save(&itemRelease)
			}
		}

		// Update item status based on release results
		if allSuccessful {
			item.ItemStatus = "success"
		} else {
			item.ItemStatus = "failed"
		}
		models.DB.Save(&item)
	}

	// Update plan status to completed
	plan.PlanStatus = "completed"
	models.DB.Save(plan)
}

// SchedulePlan schedules a plan for future execution
func (ps *PlanService) SchedulePlan(planID uint, scheduleTime time.Time, username string) error {
	// Get plan
	plan, err := ps.GetPlanByID(planID)
	if err != nil {
		return err
	}

	// Check if plan can be scheduled
	if plan.PlanStatus == "in_progress" {
		return errors.New("plan is already in progress")
	}
	if plan.PlanStatus == "completed" {
		return errors.New("plan is already completed")
	}

	// Update plan with scheduled time
	plan.PlanStartTime = int(scheduleTime.Unix())
	plan.PlanStatus = "scheduled"
	plan.Updater = username
	
	return models.DB.Save(plan).Error
	
	// Note: In a real implementation, you would need a scheduler service
	// that periodically checks for scheduled plans and executes them
	// when their scheduled time arrives.
}
