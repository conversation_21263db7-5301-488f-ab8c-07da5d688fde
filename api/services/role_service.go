package services

import (
	"errors"
	"fmt"
	"time"

	"github.com/privasea/motivus/models"
	"gorm.io/gorm"
)

// RoleService handles role and permission management
type RoleService struct{}

// 定义系统角色常量
const (
	RoleAdmin     = "admin"
	RoleTester    = "tester"
	RoleOps       = "ops"
	RoleDeveloper = "developer"
	RoleTeamLead  = "team_lead"
)

// 定义资源类型常量
const (
	ResourceProject   = "project"
	ResourceRelease   = "release"
	ResourceChart     = "chart"
	ResourceSecret    = "secret"
	ResourceConfigMap = "configmap"
	ResourceCluster   = "cluster"
	ResourceUser      = "user"
	ResourceRole      = "role"
)

// 定义操作类型常量
const (
	ActionCreate = "create"
	ActionRead   = "read"
	ActionUpdate = "update"
	ActionDelete = "delete"
	ActionManage = "manage" // 特殊权限，包含所有操作
)

// InitRolesAndPermissions initializes the default roles and permissions in the system
func (s *RoleService) InitRolesAndPermissions() error {
	// 检查是否已经初始化
	var count int64
	if err := models.DB.Model(&models.Role{}).Count(&count).Error; err != nil {
		return err
	}

	// 如果已经有角色，则跳过初始化
	if count > 0 {
		return nil
	}

	// 开始事务
	tx := models.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建权限
	permissions := []models.Permission{
		// 项目权限
		{Name: "project_create", Description: "创建项目", Resource: ResourceProject, Action: ActionCreate},
		{Name: "project_read", Description: "查看项目", Resource: ResourceProject, Action: ActionRead},
		{Name: "project_update", Description: "更新项目", Resource: ResourceProject, Action: ActionUpdate},
		{Name: "project_delete", Description: "删除项目", Resource: ResourceProject, Action: ActionDelete},
		{Name: "project_manage_members", Description: "管理项目成员", Resource: ResourceProject, Action: ActionManage},

		// 发布权限
		{Name: "release_create", Description: "创建发布", Resource: ResourceRelease, Action: ActionCreate},
		{Name: "release_read", Description: "查看发布", Resource: ResourceRelease, Action: ActionRead},
		{Name: "release_update", Description: "更新发布", Resource: ResourceRelease, Action: ActionUpdate},
		{Name: "release_delete", Description: "删除发布", Resource: ResourceRelease, Action: ActionDelete},
		{Name: "release_rollback", Description: "回滚发布", Resource: ResourceRelease, Action: "rollback"},

		// Chart权限
		{Name: "chart_create", Description: "创建Chart", Resource: ResourceChart, Action: ActionCreate},
		{Name: "chart_read", Description: "查看Chart", Resource: ResourceChart, Action: ActionRead},
		{Name: "chart_update", Description: "更新Chart", Resource: ResourceChart, Action: ActionUpdate},
		{Name: "chart_delete", Description: "删除Chart", Resource: ResourceChart, Action: ActionDelete},

		// Secret权限
		{Name: "secret_create", Description: "创建Secret", Resource: ResourceSecret, Action: ActionCreate},
		{Name: "secret_read", Description: "查看Secret", Resource: ResourceSecret, Action: ActionRead},
		{Name: "secret_update", Description: "更新Secret", Resource: ResourceSecret, Action: ActionUpdate},
		{Name: "secret_delete", Description: "删除Secret", Resource: ResourceSecret, Action: ActionDelete},

		// ConfigMap权限
		{Name: "configmap_create", Description: "创建ConfigMap", Resource: ResourceConfigMap, Action: ActionCreate},
		{Name: "configmap_read", Description: "查看ConfigMap", Resource: ResourceConfigMap, Action: ActionRead},
		{Name: "configmap_update", Description: "更新ConfigMap", Resource: ResourceConfigMap, Action: ActionUpdate},
		{Name: "configmap_delete", Description: "删除ConfigMap", Resource: ResourceConfigMap, Action: ActionDelete},

		// 集群权限
		{Name: "cluster_create", Description: "创建集群", Resource: ResourceCluster, Action: ActionCreate},
		{Name: "cluster_read", Description: "查看集群", Resource: ResourceCluster, Action: ActionRead},
		{Name: "cluster_update", Description: "更新集群", Resource: ResourceCluster, Action: ActionUpdate},
		{Name: "cluster_delete", Description: "删除集群", Resource: ResourceCluster, Action: ActionDelete},

		// 用户权限
		{Name: "user_create", Description: "创建用户", Resource: ResourceUser, Action: ActionCreate},
		{Name: "user_read", Description: "查看用户", Resource: ResourceUser, Action: ActionRead},
		{Name: "user_update", Description: "更新用户", Resource: ResourceUser, Action: ActionUpdate},
		{Name: "user_delete", Description: "删除用户", Resource: ResourceUser, Action: ActionDelete},

		// 角色权限
		{Name: "role_create", Description: "创建角色", Resource: ResourceRole, Action: ActionCreate},
		{Name: "role_read", Description: "查看角色", Resource: ResourceRole, Action: ActionRead},
		{Name: "role_update", Description: "更新角色", Resource: ResourceRole, Action: ActionUpdate},
		{Name: "role_delete", Description: "删除角色", Resource: ResourceRole, Action: ActionDelete},
		{Name: "role_assign", Description: "分配角色", Resource: ResourceRole, Action: "assign"},
	}

	for _, perm := range permissions {
		now := time.Now().Unix()
		perm.CreateTime = int(now)
		perm.UpdateTime = int(now)
		if err := tx.Create(&perm).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 创建角色
	roles := []models.Role{
		{
			Name:        RoleAdmin,
			Description: "管理员：可以创建项目、添加/删除项目成员、管理所有资源",
			IsSystem:    true,
		},
		{
			Name:        RoleTester,
			Description: "测试人员：可以发布和回滚有权限的项目",
			IsSystem:    true,
		},
		{
			Name:        RoleOps,
			Description: "运维人员：可以创建和修改 release 的 chart 版本",
			IsSystem:    true,
		},
		{
			Name:        RoleDeveloper,
			Description: "开发人员：具有基本的查看权限",
			IsSystem:    true,
		},
		{
			Name:        RoleTeamLead,
			Description: "团队负责人：可以进行项目授权管理",
			IsSystem:    true,
		},
	}

	for _, role := range roles {
		now := time.Now().Unix()
		role.CreateTime = int(now)
		role.UpdateTime = int(now)
		if err := tx.Create(&role).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 获取所有权限和角色
	var allPermissions []models.Permission
	if err := tx.Find(&allPermissions).Error; err != nil {
		tx.Rollback()
		return err
	}

	var allRoles []models.Role
	if err := tx.Find(&allRoles).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 创建权限映射，方便查找
	permMap := make(map[string]uint)
	for _, p := range allPermissions {
		permMap[p.Name] = p.ID
	}

	// 创建角色映射，方便查找
	roleMap := make(map[string]uint)
	for _, r := range allRoles {
		roleMap[r.Name] = r.ID
	}

	// 为管理员分配所有权限
	for _, p := range allPermissions {
		rolePermission := models.RolePermission{
			RoleID:       roleMap[RoleAdmin],
			PermissionID: p.ID,
		}
		if err := tx.Create(&rolePermission).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 为测试人员分配权限
	testerPermissions := []string{
		"project_read", "release_read", "release_create", "release_update", "release_rollback",
		"chart_read", "secret_read", "configmap_read",
	}
	for _, permName := range testerPermissions {
		if permID, ok := permMap[permName]; ok {
			rolePermission := models.RolePermission{
				RoleID:       roleMap[RoleTester],
				PermissionID: permID,
			}
			if err := tx.Create(&rolePermission).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 为运维人员分配权限
	opsPermissions := []string{
		"project_read", "release_read", "release_create", "release_update", "release_delete",
		"chart_create", "chart_read", "chart_update", "chart_delete",
		"secret_create", "secret_read", "secret_update", "secret_delete",
		"configmap_create", "configmap_read", "configmap_update", "configmap_delete",
		"cluster_read",
	}
	for _, permName := range opsPermissions {
		if permID, ok := permMap[permName]; ok {
			rolePermission := models.RolePermission{
				RoleID:       roleMap[RoleOps],
				PermissionID: permID,
			}
			if err := tx.Create(&rolePermission).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 为开发人员分配权限
	devPermissions := []string{
		"project_read", "release_read", "chart_read", "secret_read", "configmap_read",
	}
	for _, permName := range devPermissions {
		if permID, ok := permMap[permName]; ok {
			rolePermission := models.RolePermission{
				RoleID:       roleMap[RoleDeveloper],
				PermissionID: permID,
			}
			if err := tx.Create(&rolePermission).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 为团队负责人分配权限
	teamLeadPermissions := []string{
		"project_read", "project_update", "project_manage_members",
		"release_read", "chart_read", "secret_read", "configmap_read",
		"role_read", "role_assign",
	}
	for _, permName := range teamLeadPermissions {
		if permID, ok := permMap[permName]; ok {
			rolePermission := models.RolePermission{
				RoleID:       roleMap[RoleTeamLead],
				PermissionID: permID,
			}
			if err := tx.Create(&rolePermission).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 提交事务
	return tx.Commit().Error
}

// GetUserRoles returns all roles assigned to a user
func (s *RoleService) GetUserRoles(username string) ([]models.Role, error) {
	var user models.User
	if err := models.DB.Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found: %s", username)
		}
		return nil, err
	}

	var roles []models.Role
	if err := models.DB.Joins("JOIN user_roles ON user_roles.role_id = roles.id").
		Where("user_roles.user_id = ?", user.ID).
		Find(&roles).Error; err != nil {
		return nil, err
	}

	return roles, nil
}

// GetUserProjectRoles returns all roles assigned to a user for a specific project
func (s *RoleService) GetUserProjectRoles(username string, projectID uint) ([]models.Role, error) {
	var user models.User
	if err := models.DB.Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found: %s", username)
		}
		return nil, err
	}

	var roles []models.Role
	if err := models.DB.Joins("JOIN project_roles ON project_roles.role_id = roles.id").
		Where("project_roles.user_id = ? AND project_roles.project_id = ?", user.ID, projectID).
		Find(&roles).Error; err != nil {
		return nil, err
	}

	return roles, nil
}

// AssignRoleToUser assigns a role to a user
func (s *RoleService) AssignRoleToUser(username string, roleName string) error {
	var user models.User
	if err := models.DB.Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found: %s", username)
		}
		return err
	}

	var role models.Role
	if err := models.DB.Where("name = ?", roleName).First(&role).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("role not found: %s", roleName)
		}
		return err
	}

	// 检查是否已经分配了该角色
	var count int64
	if err := models.DB.Model(&models.UserRole{}).
		Where("user_id = ? AND role_id = ?", user.ID, role.ID).
		Count(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		return nil // 已经分配了该角色，不需要重复分配
	}

	userRole := models.UserRole{
		UserID: user.ID,
		RoleID: role.ID,
	}

	return models.DB.Create(&userRole).Error
}

// AssignRoleToUserForProject assigns a role to a user for a specific project
func (s *RoleService) AssignRoleToUserForProject(username string, roleName string, projectID uint, grantedBy string) error {
	var user models.User
	if err := models.DB.Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found: %s", username)
		}
		return err
	}

	var role models.Role
	if err := models.DB.Where("name = ?", roleName).First(&role).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("role not found: %s", roleName)
		}
		return err
	}

	var project models.Project
	if err := models.DB.First(&project, projectID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("project not found: ID %d", projectID)
		}
		return err
	}

	var grantedByUser models.User
	if err := models.DB.Where("username = ?", grantedBy).First(&grantedByUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("granting user not found: %s", grantedBy)
		}
		return err
	}

	// 检查是否已经分配了该角色
	var count int64
	if err := models.DB.Model(&models.ProjectRole{}).
		Where("user_id = ? AND role_id = ? AND project_id = ?", user.ID, role.ID, projectID).
		Count(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		return nil // 已经分配了该角色，不需要重复分配
	}

	now := time.Now().Unix()
	projectRole := models.ProjectRole{
		BaseModel: models.BaseModel{
			CreateTime: int(now),
			UpdateTime: int(now),
		},
		ProjectID:     projectID,
		UserID:        user.ID,
		RoleID:        role.ID,
		GrantedByID:   grantedByUser.ID,
		GrantedByName: grantedBy,
	}

	return models.DB.Create(&projectRole).Error
}

// HasPermission checks if a user has a specific permission
func (s *RoleService) HasPermission(username string, permissionName string) (bool, error) {
	var user models.User
	if err := models.DB.Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, fmt.Errorf("user not found: %s", username)
		}
		return false, err
	}

	var permission models.Permission
	if err := models.DB.Where("name = ?", permissionName).First(&permission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, fmt.Errorf("permission not found: %s", permissionName)
		}
		return false, err
	}

	// 检查用户是否有该权限
	var count int64
	if err := models.DB.Model(&models.Permission{}).
		Joins("JOIN role_permissions ON role_permissions.permission_id = permissions.id").
		Joins("JOIN user_roles ON user_roles.role_id = role_permissions.role_id").
		Where("user_roles.user_id = ? AND permissions.id = ?", user.ID, permission.ID).
		Count(&count).Error; err != nil {
		return false, err
	}

	return count > 0, nil
}

// HasProjectPermission checks if a user has a specific permission for a project
func (s *RoleService) HasProjectPermission(username string, projectID uint, permissionName string) (bool, error) {
	// 首先检查用户是否有全局权限
	hasGlobalPermission, err := s.HasPermission(username, permissionName)
	if err != nil {
		return false, err
	}

	if hasGlobalPermission {
		return true, nil
	}

	var user models.User
	if err := models.DB.Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, fmt.Errorf("user not found: %s", username)
		}
		return false, err
	}

	var permission models.Permission
	if err := models.DB.Where("name = ?", permissionName).First(&permission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, fmt.Errorf("permission not found: %s", permissionName)
		}
		return false, err
	}

	// 检查用户是否有项目特定的权限
	var count int64
	if err := models.DB.Model(&models.Permission{}).
		Joins("JOIN role_permissions ON role_permissions.permission_id = permissions.id").
		Joins("JOIN project_roles ON project_roles.role_id = role_permissions.role_id").
		Where("project_roles.user_id = ? AND project_roles.project_id = ? AND permissions.id = ?",
			user.ID, projectID, permission.ID).
		Count(&count).Error; err != nil {
		return false, err
	}

	return count > 0, nil
}

// GetAllRoles returns all roles in the system
func (s *RoleService) GetAllRoles() ([]models.Role, error) {
	var roles []models.Role
	if err := models.DB.Find(&roles).Error; err != nil {
		return nil, err
	}
	return roles, nil
}

// GetRolePermissions returns all permissions assigned to a role
func (s *RoleService) GetRolePermissions(roleName string) ([]models.Permission, error) {
	var role models.Role
	if err := models.DB.Where("name = ?", roleName).First(&role).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("role not found: %s", roleName)
		}
		return nil, err
	}

	var permissions []models.Permission
	if err := models.DB.Joins("JOIN role_permissions ON role_permissions.permission_id = permissions.id").
		Where("role_permissions.role_id = ?", role.ID).
		Find(&permissions).Error; err != nil {
		return nil, err
	}

	return permissions, nil
}
