package services

import (
	"github.com/privasea/motivus/pkg/apisix"
)

// RouteService handles business logic for API route management
type RouteService struct {
	apisixClient *apisix.APISIXClient
}

// NewRouteService creates a new RouteService
func NewRouteService() *RouteService {
	return &RouteService{
		apisixClient: apisix.NewAPISIXClient(),
	}
}

// CreateRoute creates a new API route
func (rs *RouteService) CreateRoute(route *apisix.Route, username string) (string, error) {
	// Add metadata to route (e.g., creator info)
	if route.Plugins == nil {
		route.Plugins = make(map[string]interface{})
	}
	
	// Add metadata to plugins
	route.Plugins["metadata"] = map[string]interface{}{
		"creator": username,
		"updater": username,
	}
	
	return rs.apisixClient.CreateRoute(route)
}

// GetRoute gets a route by ID
func (rs *RouteService) GetRoute(id string) (*apisix.Route, error) {
	return rs.apisixClient.GetRoute(id)
}

// UpdateRoute updates a route
func (rs *RouteService) UpdateRoute(route *apisix.Route, username string) error {
	// Get existing route to preserve metadata
	existingRoute, err := rs.apisixClient.GetRoute(route.ID)
	if err != nil {
		return err
	}
	
	// Update metadata
	if existingRoute.Plugins != nil && existingRoute.Plugins["metadata"] != nil {
		metadata := existingRoute.Plugins["metadata"].(map[string]interface{})
		metadata["updater"] = username
		route.Plugins["metadata"] = metadata
	} else {
		if route.Plugins == nil {
			route.Plugins = make(map[string]interface{})
		}
		route.Plugins["metadata"] = map[string]interface{}{
			"creator": username,
			"updater": username,
		}
	}
	
	return rs.apisixClient.UpdateRoute(route.ID, route)
}

// DeleteRoute deletes a route
func (rs *RouteService) DeleteRoute(id, username string) error {
	return rs.apisixClient.DeleteRoute(id)
}

// ListRoutes lists all routes
func (rs *RouteService) ListRoutes() ([]apisix.Route, error) {
	return rs.apisixClient.ListRoutes()
}

// CreateUpstream creates a new upstream
func (rs *RouteService) CreateUpstream(upstream *apisix.Upstream, username string) (string, error) {
	// In a real implementation, you might want to add metadata to the upstream
	return rs.apisixClient.CreateUpstream(upstream)
}

// GetUpstream gets an upstream by ID
func (rs *RouteService) GetUpstream(id string) (*apisix.Upstream, error) {
	return rs.apisixClient.GetUpstream(id)
}

// UpdateUpstream updates an upstream
func (rs *RouteService) UpdateUpstream(upstream *apisix.Upstream, username string) error {
	return rs.apisixClient.UpdateUpstream(upstream.ID, upstream)
}

// DeleteUpstream deletes an upstream
func (rs *RouteService) DeleteUpstream(id, username string) error {
	return rs.apisixClient.DeleteUpstream(id)
}

// ListUpstreams lists all upstreams
func (rs *RouteService) ListUpstreams() ([]apisix.Upstream, error) {
	return rs.apisixClient.ListUpstreams()
}
