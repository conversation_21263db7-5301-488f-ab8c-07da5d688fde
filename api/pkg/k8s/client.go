package k8s

import (
	"context"
	"fmt"
	"io/ioutil"
	"strings"

	"github.com/privasea/motivus/models"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

// K8sClient is a client for interacting with Kubernetes API
type K8sClient struct {
	clientset *kubernetes.Clientset
	cluster   string
}

// NewK8sClient creates a new Kubernetes client for the default cluster
func NewK8sClient() (*K8sClient, error) {
	// Find default cluster from database
	var defaultCluster models.Cluster
	if err := models.DB.Where("is_default = ?", true).First(&defaultCluster).Error; err != nil {
		// If no default cluster found in database, try to use the first one
		if err := models.DB.First(&defaultCluster).Error; err != nil {
			return nil, fmt.Errorf("no clusters found in database")
		}
	}

	return NewK8sClientForCluster(defaultCluster.Name)
}

// NewK8sClientForCluster creates a new Kubernetes client for a specific cluster
func NewK8sClientForCluster(clusterName string) (*K8sClient, error) {
	var k8sConfig *rest.Config
	var err error

	fmt.Printf("Creating K8s client for cluster: %s\n", clusterName)

	// Try to find the cluster in database
	var dbCluster models.Cluster
	if err := models.DB.Where("name = ?", clusterName).First(&dbCluster).Error; err != nil {
		return nil, fmt.Errorf("cluster %s not found in database", clusterName)
	}

	// Create rest config from database cluster
	k8sConfig = &rest.Config{
		Host: dbCluster.Server,
		TLSClientConfig: rest.TLSClientConfig{
			Insecure: dbCluster.Insecure,
		},
	}

	// Set token if provided
	if dbCluster.Token != "" {
		k8sConfig.BearerToken = dbCluster.Token
		fmt.Printf("Using token for cluster %s (length: %d)\n", dbCluster.Name, len(dbCluster.Token))
	} else {
		fmt.Printf("No token configured for cluster %s\n", dbCluster.Name)
	}

	// Set CA data or file
	if dbCluster.CAData != "" {
		// Format CA data to ensure proper PEM structure
		caData, err := formatCACertificate(dbCluster.CAData)
		if err != nil {
			return nil, fmt.Errorf("failed to format CA certificate: %w", err)
		}

		k8sConfig.TLSClientConfig.CAData = []byte(caData)
		fmt.Printf("Using formatted CA data for cluster %s (length: %d)\n", clusterName, len(caData))
	} else if dbCluster.CAFile != "" {
		caData, err := ioutil.ReadFile(dbCluster.CAFile)
		if err != nil {
			return nil, fmt.Errorf("failed to read CA file: %w", err)
		}
		k8sConfig.TLSClientConfig.CAData = caData
		fmt.Printf("Using CA data from file for cluster %s\n", clusterName)
	}

	// Create clientset
	clientset, err := kubernetes.NewForConfig(k8sConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create Kubernetes clientset: %w", err)
	}

	return &K8sClient{
		clientset: clientset,
		cluster:   clusterName,
	}, nil
}

// GetClientset returns the Kubernetes clientset
func (c *K8sClient) GetClientset() *kubernetes.Clientset {
	return c.clientset
}

// GetCluster returns the cluster name
func (c *K8sClient) GetCluster() string {
	return c.cluster
}

// CreateSecret creates a new Secret
func (c *K8sClient) CreateSecret(ctx context.Context, secret *corev1.Secret) (*corev1.Secret, error) {
	return c.clientset.CoreV1().Secrets(secret.Namespace).Create(ctx, secret, metav1.CreateOptions{})
}

// UpdateSecret updates an existing Secret
func (c *K8sClient) UpdateSecret(ctx context.Context, secret *corev1.Secret) (*corev1.Secret, error) {
	return c.clientset.CoreV1().Secrets(secret.Namespace).Update(ctx, secret, metav1.UpdateOptions{})
}

// GetSecret gets a Secret by name and namespace
func (c *K8sClient) GetSecret(ctx context.Context, name, namespace string) (*corev1.Secret, error) {
	return c.clientset.CoreV1().Secrets(namespace).Get(ctx, name, metav1.GetOptions{})
}

// DeleteSecret deletes a Secret
func (c *K8sClient) DeleteSecret(ctx context.Context, name, namespace string) error {
	return c.clientset.CoreV1().Secrets(namespace).Delete(ctx, name, metav1.DeleteOptions{})
}

// ListSecrets lists all Secrets in a namespace
func (c *K8sClient) ListSecrets(ctx context.Context, namespace string) (*corev1.SecretList, error) {
	return c.clientset.CoreV1().Secrets(namespace).List(ctx, metav1.ListOptions{})
}

// GetConfigMap gets a ConfigMap by name and namespace
func (c *K8sClient) GetConfigMap(ctx context.Context, name, namespace string) (*corev1.ConfigMap, error) {
	return c.clientset.CoreV1().ConfigMaps(namespace).Get(ctx, name, metav1.GetOptions{})
}

// CreateConfigMap creates a new ConfigMap
func (c *K8sClient) CreateConfigMap(ctx context.Context, configMap *corev1.ConfigMap) (*corev1.ConfigMap, error) {
	return c.clientset.CoreV1().ConfigMaps(configMap.Namespace).Create(ctx, configMap, metav1.CreateOptions{})
}

// UpdateConfigMap updates an existing ConfigMap
func (c *K8sClient) UpdateConfigMap(ctx context.Context, configMap *corev1.ConfigMap) (*corev1.ConfigMap, error) {
	return c.clientset.CoreV1().ConfigMaps(configMap.Namespace).Update(ctx, configMap, metav1.UpdateOptions{})
}

// DeleteConfigMap deletes a ConfigMap
func (c *K8sClient) DeleteConfigMap(ctx context.Context, name, namespace string) error {
	return c.clientset.CoreV1().ConfigMaps(namespace).Delete(ctx, name, metav1.DeleteOptions{})
}

// ListConfigMaps lists all ConfigMaps in a namespace
func (c *K8sClient) ListConfigMaps(ctx context.Context, namespace string) (*corev1.ConfigMapList, error) {
	return c.clientset.CoreV1().ConfigMaps(namespace).List(ctx, metav1.ListOptions{})
}

// ListNamespaces lists all namespaces
func (c *K8sClient) ListNamespaces(ctx context.Context) (*corev1.NamespaceList, error) {
	return c.clientset.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
}

// formatCACertificate formats CA certificate data to ensure proper PEM structure
func formatCACertificate(rawCAData string) (string, error) {
	if rawCAData == "" {
		return "", nil
	}

	// Clean up the input - remove all whitespace and newlines
	cleanData := strings.ReplaceAll(rawCAData, " ", "")
	cleanData = strings.ReplaceAll(cleanData, "\n", "")
	cleanData = strings.ReplaceAll(cleanData, "\r", "")
	cleanData = strings.ReplaceAll(cleanData, "\t", "")

	// Check if it contains the certificate markers
	if !strings.Contains(cleanData, "-----BEGINCERTIFICATE-----") || !strings.Contains(cleanData, "-----ENDCERTIFICATE-----") {
		return "", fmt.Errorf("invalid certificate format: missing BEGIN or END markers")
	}

	// Find the certificate content
	beginMarker := "-----BEGINCERTIFICATE-----"
	endMarker := "-----ENDCERTIFICATE-----"

	beginIndex := strings.Index(cleanData, beginMarker)
	endIndex := strings.Index(cleanData, endMarker)

	if beginIndex == -1 || endIndex == -1 || endIndex <= beginIndex {
		return "", fmt.Errorf("invalid certificate format: malformed markers")
	}

	// Extract the base64 certificate content
	certContent := cleanData[beginIndex+len(beginMarker) : endIndex]

	// Validate that the content is base64-like (contains only valid base64 characters)
	validBase64Chars := "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
	for _, char := range certContent {
		if !strings.ContainsRune(validBase64Chars, char) {
			return "", fmt.Errorf("invalid certificate content: contains non-base64 characters")
		}
	}

	// Format the certificate with proper line breaks
	var formattedCert strings.Builder
	formattedCert.WriteString("-----BEGIN CERTIFICATE-----\n")

	// Add line breaks every 64 characters
	for i := 0; i < len(certContent); i += 64 {
		end := i + 64
		if end > len(certContent) {
			end = len(certContent)
		}
		formattedCert.WriteString(certContent[i:end])
		formattedCert.WriteString("\n")
	}

	formattedCert.WriteString("-----END CERTIFICATE-----\n")

	return formattedCert.String(), nil
}

// min returns the smaller of x or y.
func min(x, y int) int {
	if x < y {
		return x
	}
	return y
}
