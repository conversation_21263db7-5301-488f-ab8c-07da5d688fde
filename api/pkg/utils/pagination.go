package utils

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

// Pagination represents pagination parameters
type Pagination struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
	Total    int `json:"total"`
}

// GetPagination gets pagination parameters from request
func GetPagination(c *gin.Context) Pagination {
	// Default values
	page := 1
	pageSize := 10

	// Parse page parameter
	pageStr := c.Query("page")
	if pageStr != "" {
		pageInt, err := strconv.Atoi(pageStr)
		if err == nil && pageInt > 0 {
			page = pageInt
		}
	}

	// Parse page_size parameter
	pageSizeStr := c.Query("page_size")
	if pageSizeStr != "" {
		pageSizeInt, err := strconv.Atoi(pageSizeStr)
		if err == nil && pageSizeInt > 0 && pageSizeInt <= 100 {
			pageSize = pageSizeInt
		}
	}

	return Pagination{
		Page:     page,
		PageSize: pageSize,
	}
}

// GetOffset calculates the offset for database queries
func (p *Pagination) GetOffset() int {
	return (p.Page - 1) * p.PageSize
}

// GetLimit returns the limit for database queries
func (p *Pagination) GetLimit() int {
	return p.PageSize
}

// SetTotal sets the total number of records
func (p *Pagination) SetTotal(total int) {
	p.Total = total
}
