package argocd

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/privasea/motivus/config"
)

// ArgoCDClient is a client for interacting with ArgoCD API
type ArgoCDClient struct {
	BaseURL  string
	Username string
	Password string
	Token    string
	Client   *http.Client
}

// Application represents an ArgoCD application
type Application struct {
	Metadata struct {
		Name      string            `json:"name"`
		Namespace string            `json:"namespace"`
		Labels    map[string]string `json:"labels,omitempty"`
	} `json:"metadata"`
	Spec struct {
		Project string `json:"project"`
		Source  struct {
			RepoURL        string `json:"repoURL"`
			Path           string `json:"path,omitempty"`
			TargetRevision string `json:"targetRevision"`
			Chart          string `json:"chart,omitempty"`
			Helm           struct {
				Parameters []struct {
					Name  string `json:"name"`
					Value string `json:"value"`
				} `json:"parameters,omitempty"`
				Values string `json:"values,omitempty"`
			} `json:"helm,omitempty"`
		} `json:"source"`
		Destination struct {
			Server    string `json:"server"`
			Namespace string `json:"namespace"`
		} `json:"destination"`
		SyncPolicy struct {
			Automated struct {
				Prune      bool `json:"prune"`
				SelfHeal   bool `json:"selfHeal"`
				AllowEmpty bool `json:"allowEmpty"`
			} `json:"automated,omitempty"`
			SyncOptions []string `json:"syncOptions,omitempty"`
		} `json:"syncPolicy,omitempty"`
	} `json:"spec"`
	Status struct {
		Health struct {
			Status string `json:"status"`
		} `json:"health"`
		Sync struct {
			Status string `json:"status"`
		} `json:"sync"`
	} `json:"status,omitempty"`
}

// ApplicationList represents a list of ArgoCD applications
type ApplicationList struct {
	Items []Application `json:"items"`
}

// NewArgoCDClient creates a new ArgoCD client
func NewArgoCDClient() *ArgoCDClient {
	return &ArgoCDClient{
		BaseURL:  config.AppConfig.ArgoCD.URL,
		Username: config.AppConfig.ArgoCD.Username,
		Password: config.AppConfig.ArgoCD.Password,
		Client:   &http.Client{},
	}
}

// Login authenticates with ArgoCD and gets a token
func (c *ArgoCDClient) Login() error {
	endpoint := "/api/v1/session"

	data := map[string]string{
		"username": c.Username,
		"password": c.Password,
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	req, err := c.newRequest("POST", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")

	var response struct {
		Token string `json:"token"`
	}

	err = c.doRequest(req, &response)
	if err != nil {
		return err
	}

	c.Token = response.Token
	return nil
}

// ListApplications lists all applications in ArgoCD
func (c *ArgoCDClient) ListApplications() ([]Application, error) {
	if c.Token == "" {
		if err := c.Login(); err != nil {
			return nil, err
		}
	}

	endpoint := "/api/v1/applications"

	req, err := c.newRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var appList ApplicationList
	err = c.doRequest(req, &appList)
	if err != nil {
		return nil, err
	}

	return appList.Items, nil
}

// GetApplication gets a specific application from ArgoCD
func (c *ArgoCDClient) GetApplication(name string) (*Application, error) {
	if c.Token == "" {
		if err := c.Login(); err != nil {
			return nil, err
		}
	}

	endpoint := fmt.Sprintf("/api/v1/applications/%s", name)

	req, err := c.newRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var app Application
	err = c.doRequest(req, &app)
	if err != nil {
		return nil, err
	}

	return &app, nil
}

// CreateApplication creates a new application in ArgoCD
func (c *ArgoCDClient) CreateApplication(app *Application) error {
	if c.Token == "" {
		if err := c.Login(); err != nil {
			return err
		}
	}

	endpoint := "/api/v1/applications"

	jsonData, err := json.Marshal(app)
	if err != nil {
		return err
	}

	req, err := c.newRequest("POST", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")

	var response Application
	err = c.doRequest(req, &response)
	if err != nil {
		return err
	}

	return nil
}

// UpdateApplication updates an existing application in ArgoCD
func (c *ArgoCDClient) UpdateApplication(app *Application) error {
	if c.Token == "" {
		if err := c.Login(); err != nil {
			return err
		}
	}

	endpoint := fmt.Sprintf("/api/v1/applications/%s", app.Metadata.Name)

	jsonData, err := json.Marshal(app)
	if err != nil {
		return err
	}

	req, err := c.newRequest("PUT", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")

	var response Application
	err = c.doRequest(req, &response)
	if err != nil {
		return err
	}

	return nil
}

// DeleteApplication deletes an application from ArgoCD
func (c *ArgoCDClient) DeleteApplication(name string) error {
	if c.Token == "" {
		if err := c.Login(); err != nil {
			return err
		}
	}

	endpoint := fmt.Sprintf("/api/v1/applications/%s", name)

	req, err := c.newRequest("DELETE", endpoint, nil)
	if err != nil {
		return err
	}

	var response interface{}
	err = c.doRequest(req, &response)
	if err != nil {
		return err
	}

	return nil
}

// SyncApplication triggers a sync for an application
func (c *ArgoCDClient) SyncApplication(name string) error {
	if c.Token == "" {
		if err := c.Login(); err != nil {
			return err
		}
	}

	endpoint := fmt.Sprintf("/api/v1/applications/%s/sync", name)

	data := map[string]interface{}{
		"prune":     true,
		"dryRun":    false,
		"strategy":  map[string]interface{}{"hook": map[string]interface{}{"force": true}},
		"resources": []interface{}{},
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	req, err := c.newRequest("POST", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")

	var response interface{}
	err = c.doRequest(req, &response)
	if err != nil {
		return err
	}

	return nil
}

// WaitForSync waits for an application to be synced
func (c *ArgoCDClient) WaitForSync(name string, timeout time.Duration) error {
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		app, err := c.GetApplication(name)
		if err != nil {
			return err
		}

		if app.Status.Sync.Status == "Synced" && app.Status.Health.Status == "Healthy" {
			return nil
		}

		time.Sleep(5 * time.Second)
	}

	return fmt.Errorf("timeout waiting for application %s to sync", name)
}

// Cluster represents a Kubernetes cluster in ArgoCD
type Cluster struct {
	Server string        `json:"server"`
	Name   string        `json:"name"`
	Config ClusterConfig `json:"config"`
}

// ClusterConfig represents the configuration for a Kubernetes cluster in ArgoCD
type ClusterConfig struct {
	BearerToken     string          `json:"bearerToken,omitempty"`
	TLSClientConfig TLSClientConfig `json:"tlsClientConfig"`
}

// TLSClientConfig represents the TLS configuration for a Kubernetes cluster in ArgoCD
type TLSClientConfig struct {
	Insecure bool   `json:"insecure,omitempty"`
	CAData   string `json:"caData,omitempty"`
}

// AddCluster adds a new cluster to ArgoCD
func (c *ArgoCDClient) AddCluster(cluster *Cluster) error {
	if c.Token == "" {
		if err := c.Login(); err != nil {
			return err
		}
	}

	endpoint := "/api/v1/clusters"

	jsonData, err := json.Marshal(cluster)
	if err != nil {
		return err
	}

	req, err := c.newRequest("POST", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")

	var response interface{}
	err = c.doRequest(req, &response)
	if err != nil {
		return err
	}

	return nil
}

// UpdateCluster updates an existing cluster in ArgoCD
func (c *ArgoCDClient) UpdateCluster(cluster *Cluster) error {
	if c.Token == "" {
		if err := c.Login(); err != nil {
			return err
		}
	}

	// URL encode the server URL as it's used as part of the path
	serverURL := url.QueryEscape(cluster.Server)
	endpoint := fmt.Sprintf("/api/v1/clusters/%s", serverURL)

	jsonData, err := json.Marshal(cluster)
	if err != nil {
		return err
	}

	req, err := c.newRequest("PUT", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")

	var response interface{}
	err = c.doRequest(req, &response)
	if err != nil {
		return err
	}

	return nil
}

// DeleteCluster removes a cluster from ArgoCD
func (c *ArgoCDClient) DeleteCluster(server string) error {
	if c.Token == "" {
		if err := c.Login(); err != nil {
			return err
		}
	}

	// URL encode the server URL as it's used as part of the path
	serverURL := url.QueryEscape(server)
	endpoint := fmt.Sprintf("/api/v1/clusters/%s", serverURL)

	req, err := c.newRequest("DELETE", endpoint, nil)
	if err != nil {
		return err
	}

	var response interface{}
	err = c.doRequest(req, &response)
	if err != nil {
		return err
	}

	return nil
}

// ListClusters lists all clusters registered in ArgoCD
func (c *ArgoCDClient) ListClusters() ([]Cluster, error) {
	if c.Token == "" {
		if err := c.Login(); err != nil {
			return nil, err
		}
	}

	endpoint := "/api/v1/clusters"

	req, err := c.newRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var response struct {
		Items []Cluster `json:"items"`
	}

	err = c.doRequest(req, &response)
	if err != nil {
		return nil, err
	}

	return response.Items, nil
}

// newRequest creates a new HTTP request
func (c *ArgoCDClient) newRequest(method, endpoint string, body io.Reader) (*http.Request, error) {
	fullURL := fmt.Sprintf("%s%s", c.BaseURL, endpoint)
	fmt.Println(fullURL)
	// 创建请求
	req, err := http.NewRequest(method, fullURL, body)
	if err != nil {
		return nil, err
	}

	// 设置认证头
	if c.Token != "" {
		req.Header.Set("Authorization", "Bearer "+c.Token)
	}

	return req, nil
}

// doRequest performs the HTTP request and unmarshals the response
func (c *ArgoCDClient) doRequest(req *http.Request, v interface{}) error {
	resp, err := c.Client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	if v != nil {
		return json.NewDecoder(resp.Body).Decode(v)
	}

	return nil
}
