package apisix

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path"

	"github.com/privasea/motivus/config"
)

// APISIXClient is a client for interacting with APISIX Admin API
type APISIXClient struct {
	BaseURL string
	APIKey  string
	Role    string
	Client  *http.Client
}

// Route represents an APISIX route
type Route struct {
	ID         string                 `json:"id,omitempty"`
	Name       string                 `json:"name"`
	URI        string                 `json:"uri"`
	Host       string                 `json:"host,omitempty"`
	Methods    []string               `json:"methods,omitempty"`
	Upstream   *Upstream              `json:"upstream,omitempty"`
	UpstreamID string                 `json:"upstream_id,omitempty"`
	Plugins    map[string]interface{} `json:"plugins,omitempty"`
	Status     int                    `json:"status,omitempty"`
}

// Upstream represents an APISIX upstream
type Upstream struct {
	ID     string `json:"id,omitempty"`
	Name   string `json:"name"`
	Type   string `json:"type"`
	Nodes  Nodes  `json:"nodes"`
	Scheme string `json:"scheme,omitempty"`
}

// Nodes represents upstream nodes
type Nodes map[string]int

// RouteResponse represents the response from APISIX API for routes
type RouteResponse struct {
	Total int              `json:"total"`
	List  map[string]Route `json:"list"`
}

// UpstreamResponse represents the response from APISIX API for upstreams
type UpstreamResponse struct {
	Total int                 `json:"total"`
	List  map[string]Upstream `json:"list"`
}

// NewAPISIXClient creates a new APISIX client
func NewAPISIXClient() *APISIXClient {
	return &APISIXClient{
		BaseURL: config.AppConfig.APISIX.AdminURL,
		APIKey:  config.AppConfig.APISIX.AdminKey,
		Role:    config.AppConfig.APISIX.AdminRole,
		Client:  &http.Client{},
	}
}

// ListRoutes lists all routes in APISIX
func (c *APISIXClient) ListRoutes() ([]Route, error) {
	endpoint := "/apisix/admin/routes"

	req, err := c.newRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var response RouteResponse
	err = c.doRequest(req, &response)
	if err != nil {
		return nil, err
	}

	routes := make([]Route, 0, len(response.List))
	for id, route := range response.List {
		route.ID = id
		routes = append(routes, route)
	}

	return routes, nil
}

// GetRoute gets a specific route from APISIX
func (c *APISIXClient) GetRoute(id string) (*Route, error) {
	endpoint := fmt.Sprintf("/apisix/admin/routes/%s", id)

	req, err := c.newRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var response struct {
		Value Route `json:"value"`
	}

	err = c.doRequest(req, &response)
	if err != nil {
		return nil, err
	}

	response.Value.ID = id
	return &response.Value, nil
}

// CreateRoute creates a new route in APISIX
func (c *APISIXClient) CreateRoute(route *Route) (string, error) {
	endpoint := "/apisix/admin/routes"

	if route.ID != "" {
		endpoint = fmt.Sprintf("%s/%s", endpoint, route.ID)
	}

	jsonData, err := json.Marshal(route)
	if err != nil {
		return "", err
	}

	req, err := c.newRequest("PUT", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}

	req.Header.Set("Content-Type", "application/json")

	var response struct {
		Key   string `json:"key"`
		Value Route  `json:"value"`
	}

	err = c.doRequest(req, &response)
	if err != nil {
		return "", err
	}

	return response.Key, nil
}

// UpdateRoute updates an existing route in APISIX
func (c *APISIXClient) UpdateRoute(id string, route *Route) error {
	endpoint := fmt.Sprintf("/apisix/admin/routes/%s", id)

	jsonData, err := json.Marshal(route)
	if err != nil {
		return err
	}

	req, err := c.newRequest("PUT", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")

	var response interface{}
	err = c.doRequest(req, &response)
	if err != nil {
		return err
	}

	return nil
}

// DeleteRoute deletes a route from APISIX
func (c *APISIXClient) DeleteRoute(id string) error {
	endpoint := fmt.Sprintf("/apisix/admin/routes/%s", id)

	req, err := c.newRequest("DELETE", endpoint, nil)
	if err != nil {
		return err
	}

	var response interface{}
	err = c.doRequest(req, &response)
	if err != nil {
		return err
	}

	return nil
}

// ListUpstreams lists all upstreams in APISIX
func (c *APISIXClient) ListUpstreams() ([]Upstream, error) {
	endpoint := "/apisix/admin/upstreams"

	req, err := c.newRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var response UpstreamResponse
	err = c.doRequest(req, &response)
	if err != nil {
		return nil, err
	}

	upstreams := make([]Upstream, 0, len(response.List))
	for id, upstream := range response.List {
		upstream.ID = id
		upstreams = append(upstreams, upstream)
	}

	return upstreams, nil
}

// GetUpstream gets a specific upstream from APISIX
func (c *APISIXClient) GetUpstream(id string) (*Upstream, error) {
	endpoint := fmt.Sprintf("/apisix/admin/upstreams/%s", id)

	req, err := c.newRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var response struct {
		Value Upstream `json:"value"`
	}

	err = c.doRequest(req, &response)
	if err != nil {
		return nil, err
	}

	response.Value.ID = id
	return &response.Value, nil
}

// CreateUpstream creates a new upstream in APISIX
func (c *APISIXClient) CreateUpstream(upstream *Upstream) (string, error) {
	endpoint := "/apisix/admin/upstreams"

	if upstream.ID != "" {
		endpoint = fmt.Sprintf("%s/%s", endpoint, upstream.ID)
	}

	jsonData, err := json.Marshal(upstream)
	if err != nil {
		return "", err
	}

	req, err := c.newRequest("PUT", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}

	req.Header.Set("Content-Type", "application/json")

	var response struct {
		Key   string   `json:"key"`
		Value Upstream `json:"value"`
	}

	err = c.doRequest(req, &response)
	if err != nil {
		return "", err
	}

	return response.Key, nil
}

// UpdateUpstream updates an existing upstream in APISIX
func (c *APISIXClient) UpdateUpstream(id string, upstream *Upstream) error {
	endpoint := fmt.Sprintf("/apisix/admin/upstreams/%s", id)

	jsonData, err := json.Marshal(upstream)
	if err != nil {
		return err
	}

	req, err := c.newRequest("PUT", endpoint, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")

	var response interface{}
	err = c.doRequest(req, &response)
	if err != nil {
		return err
	}

	return nil
}

// DeleteUpstream deletes an upstream from APISIX
func (c *APISIXClient) DeleteUpstream(id string) error {
	endpoint := fmt.Sprintf("/apisix/admin/upstreams/%s", id)

	req, err := c.newRequest("DELETE", endpoint, nil)
	if err != nil {
		return err
	}

	var response interface{}
	err = c.doRequest(req, &response)
	if err != nil {
		return err
	}

	return nil
}

// newRequest creates a new HTTP request
func (c *APISIXClient) newRequest(method, endpoint string, body io.Reader) (*http.Request, error) {
	// 解析基础URL
	u, err := url.Parse(c.BaseURL)
	if err != nil {
		return nil, err
	}

	// 保存原始协议
	originalScheme := u.Scheme

	// 构建完整路径
	fullPath := path.Join(u.Path, endpoint)

	// 手动构建URL，确保使用原始协议
	// 格式: scheme://host[:port]/path
	port := ""
	if u.Port() != "" {
		port = ":" + u.Port()
	}

	fullURL := fmt.Sprintf("%s://%s%s%s", originalScheme, u.Hostname(), port, fullPath)

	// 创建请求
	req, err := http.NewRequest(method, fullURL, body)
	if err != nil {
		return nil, err
	}

	// 设置API密钥
	req.Header.Set("X-API-KEY", c.APIKey)

	return req, nil
}

// doRequest performs the HTTP request and unmarshals the response
func (c *APISIXClient) doRequest(req *http.Request, v interface{}) error {
	resp, err := c.Client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	if v != nil {
		return json.NewDecoder(resp.Body).Decode(v)
	}

	return nil
}
