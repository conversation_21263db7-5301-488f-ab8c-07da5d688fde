package helm

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path"

	"github.com/privasea/motivus/config"
)

// ChartMuseumClient is a client for interacting with ChartMuseum
type ChartMuseumClient struct {
	BaseURL  string
	Username string
	Password string
	Client   *http.Client
}

// ChartVersion represents a chart version in ChartMuseum
type ChartVersion struct {
	Name        string            `json:"name"`
	Version     string            `json:"version"`
	Description string            `json:"description"`
	APIVersion  string            `json:"apiVersion"`
	AppVersion  string            `json:"appVersion"`
	Type        string            `json:"type"`
	Created     string            `json:"created"`
	Digest      string            `json:"digest"`
	URLs        []string          `json:"urls"`
	Metadata    map[string]string `json:"metadata"`
}

// ChartResponse represents the response from ChartMuseum API
type ChartResponse struct {
	Name     string         `json:"name"`
	Total    int            `json:"total"`
	Versions []ChartVersion `json:"versions"`
}

// NewChartMuseumClient creates a new ChartMuseum client
func NewChartMuseumClient() *ChartMuseumClient {
	return &ChartMuseumClient{
		BaseURL:  config.AppConfig.Helm.ChartMuseumURL,
		Username: config.AppConfig.Helm.ChartMuseumUsername,
		Password: config.AppConfig.Helm.ChartMuseumPassword,
		Client:   &http.Client{},
	}
}

// ListCharts lists all charts in the ChartMuseum
func (c *ChartMuseumClient) ListCharts() ([]ChartResponse, error) {
	endpoint := "/api/charts"

	req, err := c.newRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var charts []ChartResponse
	err = c.doRequest(req, &charts)
	if err != nil {
		return nil, err
	}

	return charts, nil
}

// GetChart gets a specific chart from ChartMuseum
func (c *ChartMuseumClient) GetChart(name string) (*ChartResponse, error) {
	endpoint := fmt.Sprintf("/api/charts/%s", name)

	req, err := c.newRequest("GET", endpoint, nil)
	if err != nil {
		return nil, err
	}

	var chart ChartResponse
	err = c.doRequest(req, &chart)
	if err != nil {
		return nil, err
	}

	return &chart, nil
}

// UploadChart uploads a chart to ChartMuseum
func (c *ChartMuseumClient) UploadChart(chartData []byte) error {
	endpoint := "/api/charts"

	req, err := c.newRequest("POST", endpoint, bytes.NewBuffer(chartData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/octet-stream")

	var response interface{}
	err = c.doRequest(req, &response)
	if err != nil {
		return err
	}

	return nil
}

// DeleteChart deletes a chart from ChartMuseum
func (c *ChartMuseumClient) DeleteChart(name, version string) error {
	endpoint := fmt.Sprintf("/api/charts/%s/%s", name, version)

	req, err := c.newRequest("DELETE", endpoint, nil)
	if err != nil {
		return err
	}

	var response interface{}
	err = c.doRequest(req, &response)
	if err != nil {
		return err
	}

	return nil
}

// newRequest creates a new HTTP request
func (c *ChartMuseumClient) newRequest(method, endpoint string, body io.Reader) (*http.Request, error) {
	// 解析基础URL
	u, err := url.Parse(c.BaseURL)
	if err != nil {
		return nil, err
	}

	// 保存原始协议
	originalScheme := u.Scheme

	// 构建完整路径
	fullPath := path.Join(u.Path, endpoint)

	// 手动构建URL，确保使用原始协议
	// 格式: scheme://host[:port]/path
	port := ""
	if u.Port() != "" {
		port = ":" + u.Port()
	}

	fullURL := fmt.Sprintf("%s://%s%s%s", originalScheme, u.Hostname(), port, fullPath)

	// 创建请求
	req, err := http.NewRequest(method, fullURL, body)
	if err != nil {
		return nil, err
	}

	// 设置基本认证
	if c.Username != "" && c.Password != "" {
		req.SetBasicAuth(c.Username, c.Password)
	}

	return req, nil
}

// doRequest performs the HTTP request and unmarshals the response
func (c *ChartMuseumClient) doRequest(req *http.Request, v interface{}) error {
	resp, err := c.Client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
	}

	if v != nil {
		return json.NewDecoder(resp.Body).Decode(v)
	}

	return nil
}
