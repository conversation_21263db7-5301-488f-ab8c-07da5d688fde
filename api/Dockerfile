FROM golang:1.23-alpine AS builder

WORKDIR /app

# Copy go.mod and go.sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o motivus .

# Use a minimal alpine image
FROM alpine:3.15

WORKDIR /app

# Copy the binary from builder
COPY --from=builder /app/motivus .
# Copy config file
COPY --from=builder /app/config/config.yaml ./config/

# Expose port
EXPOSE 8080

# Run the application
CMD ["./motivus", "--config", "config/config.yaml"]
