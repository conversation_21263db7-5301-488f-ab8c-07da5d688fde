package middleware

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/privasea/motivus/services"
)

// RequirePermission creates a middleware that checks if the user has the required permission
func RequirePermission(permissionName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		username := c.GetString("username")
		if username == "" {
			c.JSO<PERSON>(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		roleService := services.RoleService{}
		hasPermission, err := roleService.HasPermission(username, permissionName)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			c.Abort()
			return
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireProjectPermission creates a middleware that checks if the user has the required permission for a project
func RequireProjectPermission(permissionName string, projectIDParam string) gin.HandlerFunc {
	return func(c *gin.Context) {
		username := c.GetString("username")
		if username == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		projectIDStr := c.Param(projectIDParam)
		projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
			c.Abort()
			return
		}

		roleService := services.RoleService{}
		hasPermission, err := roleService.HasProjectPermission(username, uint(projectID), permissionName)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			c.Abort()
			return
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied for this project"})
			c.Abort()
			return
		}

		c.Next()
	}
}
