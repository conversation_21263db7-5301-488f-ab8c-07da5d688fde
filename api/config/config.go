package config

import (
	"fmt"
	"log"

	"github.com/spf13/viper"
)

type Config struct {
	Server     ServerConfig     `mapstructure:"server"`
	Database   DatabaseConfig   `mapstructure:"database"`
	Redis      RedisConfig      `mapstructure:"redis"`
	JWT        JWTConfig        `mapstructure:"jwt"`
	ArgoCD     ArgoCDConfig     `mapstructure:"argocd"`
	Helm       HelmConfig       `mapstructure:"helm"`
	APISIX     APISIXConfig     `mapstructure:"apisix"`
}

type ServerConfig struct {
	Port int    `mapstructure:"port"`
	Mode string `mapstructure:"mode"`
}

type DatabaseConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	DBName   string `mapstructure:"dbname"`
}

type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

type JWTConfig struct {
	Secret    string `mapstructure:"secret"`
	ExpiresIn int    `mapstructure:"expires_in"` // in hours
}

type ArgoCDConfig struct {
	URL      string `mapstructure:"url"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
}

type HelmConfig struct {
	ChartMuseumURL      string `mapstructure:"chartmuseum_url"`
	ChartMuseumUsername string `mapstructure:"chartmuseum_username"`
	ChartMuseumPassword string `mapstructure:"chartmuseum_password"`
	ChartRepoURL        string `mapstructure:"chart_repo_url"`
}

type APISIXConfig struct {
	AdminURL  string `mapstructure:"admin_url"`
	AdminKey  string `mapstructure:"admin_key"`
	AdminRole string `mapstructure:"admin_role"`
}

var AppConfig Config

func LoadConfig(configPath string) error {
	viper.SetConfigFile(configPath)
	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	if err := viper.Unmarshal(&AppConfig); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	log.Println("Configuration loaded successfully")
	log.Printf("config: %v", AppConfig)
	return nil
}
