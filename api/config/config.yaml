server:
  port: 8080
  mode: debug  # debug, release, test

database:
  host: localhost  # 连接到本地Docker中的MySQL服务
  port: 3306
  username: root
  password: password
  dbname: motivus

redis:
  host: localhost  # 连接到本地Docker中的Redis服务
  port: 6379
  password: ""
  db: 0

jwt:
  secret: "motivus-secret-key-for-development"
  expires_in: 24  # hours

argocd:
  # 使用外部自建的ArgoCD服务
  url: "http://***********"  # 请替换为您自建的ArgoCD服务地址
  username: "admin"
  password: "QR0BTMb-6deICp9S"  # 请替换为您的ArgoCD管理员密码

helm:
  # 使用外部自建的ChartMuseum服务
  chartmuseum_url: "http://***********"  # 请替换为您自建的ChartMuseum服务地址
  chartmuseum_username: "admin"
  chartmuseum_password: "chartmuseum"
  chart_repo_url: "http://***********"  # 请替换为您自建的ChartMuseum服务地址

apisix:
  # 使用外部自建的APISIX服务
  admin_url: "http://***********"
  admin_key: "edd1c9f034335f136f87ad84b625c8f1"
  admin_role: "admin"
