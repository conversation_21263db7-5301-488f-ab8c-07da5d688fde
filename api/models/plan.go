package models

// ReleasePlan represents the release_plan table
type ReleasePlan struct {
	ID            uint   `gorm:"primaryKey" json:"id"`
	PlanName      string `gorm:"column:plan_name;type:varchar(255);not null" json:"plan_name"`
	PlanStatus    string `gorm:"column:plan_status;type:varchar(255);not null" json:"plan_status"`
	PlanStartTime int    `gorm:"column:plan_start_time;not null;default:0" json:"plan_start_time"`
	Creater       string `gorm:"column:creater;type:varchar(255);not null" json:"creater"`
	Updater       string `gorm:"column:updater;type:varchar(255);not null" json:"updater"`
	CreateTime    int    `gorm:"column:create_time;not null;default:0" json:"create_time"`
	UpdateTime    int    `gorm:"column:update_time;not null;default:0" json:"update_time"`
}

// TableName specifies the table name for ReleasePlan
func (ReleasePlan) TableName() string {
	return "release_plan"
}

// PlanItem represents the plan_item table
type PlanItem struct {
	ID           uint   `gorm:"primaryKey" json:"id"`
	PlanID       uint   `gorm:"column:plan_id;not null;index" json:"plan_id"`
	ProjectID    uint   `gorm:"column:project_id;not null" json:"project_id"`
	ItemType     string `gorm:"column:item_type;type:varchar(255);not null" json:"item_type"`
	ItemSeq      int    `gorm:"column:item_seq;not null" json:"item_seq"`
	ItemStatus   string `gorm:"column:item_status;type:varchar(255);not null" json:"item_status"`
	DingAuditID  string `gorm:"column:ding_audit_id;type:varchar(255);not null" json:"ding_audit_id"`
	Creater      string `gorm:"column:creater;type:varchar(255);not null" json:"creater"`
	CreateTime   int    `gorm:"column:create_time;not null;default:0" json:"create_time"`
	Updater      string `gorm:"column:updater;type:varchar(255);not null" json:"updater"`
	UpdateTime   int    `gorm:"column:update_time;not null;default:0" json:"update_time"`
	Deleted      bool   `gorm:"column:deleted;default:false" json:"deleted"`
	IssueNo      string `gorm:"column:issue_no;type:varchar(255);not null" json:"issue_no"`
	Tester       string `gorm:"column:tester;type:varchar(255);not null" json:"tester"`
	PMName       string `gorm:"column:pm_name;type:varchar(255);not null" json:"pm_name"`
	ByroadChange string `gorm:"column:byroad_change;type:varchar(255);not null" json:"byroad_change"`
	MemanChange  string `gorm:"column:meman_change;type:varchar(255);not null" json:"meman_change"`
	DBChange     string `gorm:"column:db_change;type:varchar(255);not null" json:"db_change"`
	DoveChange   string `gorm:"column:dove_change;type:varchar(255);not null" json:"dove_change"`
	OtherChange  string `gorm:"column:other_change;type:varchar(255);not null" json:"other_change"`
	Comment      string `gorm:"column:comment;type:varchar(255);not null" json:"comment"`
}

// TableName specifies the table name for PlanItem
func (PlanItem) TableName() string {
	return "plan_item"
}

// PlanItemRelease represents the plan_item_release table
type PlanItemRelease struct {
	ID            uint   `gorm:"primaryKey" json:"id"`
	PlanItemID    uint   `gorm:"column:plan_item_id;not null;index" json:"plan_item_id"`
	ReleaseID     uint   `gorm:"column:release_id;not null" json:"release_id"`
	TargetTag     string `gorm:"column:target_tag;type:varchar(255);not null" json:"target_tag"`
	RollbackTag   string `gorm:"column:rollback_tag;type:varchar(255);not null" json:"rollback_tag"`
	ReleaseStatus string `gorm:"column:release_status;type:varchar(255);not null" json:"release_status"`
	ErrMsg        string `gorm:"column:err_msg;type:varchar(255);not null" json:"err_msg"`
	ChangeLog     string `gorm:"column:change_log;type:varchar(255);not null" json:"change_log"`
}

// TableName specifies the table name for PlanItemRelease
func (PlanItemRelease) TableName() string {
	return "plan_item_release"
}

// DingdingUserID represents the dingding_userid table
type DingdingUserID struct {
	ID         uint   `gorm:"primaryKey" json:"id"`
	Phone      string `gorm:"column:phone;type:varchar(255);not null;uniqueIndex" json:"phone"`
	AuthUID    string `gorm:"column:auth_uid;type:varchar(255);not null" json:"auth_uid"`
	DingUserID string `gorm:"column:ding_user_id;type:varchar(255);not null" json:"ding_user_id"`
	CreateTime int    `gorm:"column:create_time;not null;default:0" json:"create_time"`
	UpdateTime int    `gorm:"column:update_time;not null;default:0" json:"update_time"`
}

// TableName specifies the table name for DingdingUserID
func (DingdingUserID) TableName() string {
	return "dingding_userid"
}
