package models

// Release represents the k8swan_release table
type Release struct {
	BaseModel
	ProjectID        uint   `gorm:"column:project_id;not null" json:"project_id"`
	ReleaseName      string `gorm:"column:release_name;type:varchar(255);not null" json:"release_name"`
	ClusterName      string `gorm:"column:cluster_name;type:varchar(255);not null" json:"cluster_name"`
	ClusterNamespace string `gorm:"column:cluster_namespace;type:varchar(255);not null" json:"cluster_namespace"`
	ChartVersion     string `gorm:"column:chart_version;type:varchar(255);not null" json:"chart_version"`
	ChartValues      string `gorm:"column:chart_values;type:text" json:"chart_values"`
	ReleaseEnv       int    `gorm:"column:release_env;not null" json:"release_env"`
	Creater          string `gorm:"column:creater;type:varchar(255);not null" json:"creater"`
}

// TableName specifies the table name for Release
func (Release) TableName() string {
	return "k8swan_release"
}

// ReleaseHistory represents the k8swan_release_history table
type ReleaseHistory struct {
	ID               uint   `gorm:"primaryKey" json:"id"`
	ProjectID        uint   `gorm:"column:project_id;not null" json:"project_id"`
	ReleaseID        uint   `gorm:"column:release_id;not null;index" json:"release_id"`
	ReleaseName      string `gorm:"column:release_name;type:varchar(255);not null" json:"release_name"`
	PrevChartVersion string `gorm:"column:prev_chart_version;type:varchar(255);not null" json:"prev_chart_version"`
	ChartVersion     string `gorm:"column:chart_version;type:varchar(255);not null" json:"chart_version"`
	PrevChartValues  string `gorm:"column:prev_chart_values;type:text" json:"prev_chart_values"`
	ChartValues      string `gorm:"column:chart_values;type:text" json:"chart_values"`
	PrevImageTag     string `gorm:"column:prev_image_tag;type:varchar(255)" json:"prev_image_tag"`
	ImageTag         string `gorm:"column:image_tag;type:varchar(255)" json:"image_tag"`
	Username         string `gorm:"column:username;type:varchar(255);not null" json:"username"`
	ChangeLog        string `gorm:"column:change_log;type:varchar(255);not null" json:"change_log"`
	HType            string `gorm:"column:h_type;type:varchar(255);not null" json:"h_type"`
	CreateTime       int    `gorm:"column:create_time;not null;default:0" json:"create_time"`
}

// TableName specifies the table name for ReleaseHistory
func (ReleaseHistory) TableName() string {
	return "k8swan_release_history"
}

// ActionLog represents the k8swan_action_log table
type ActionLog struct {
	ID               uint   `gorm:"primaryKey" json:"id"`
	Username         string `gorm:"column:username;type:varchar(255);not null;index" json:"username"`
	ActionCode       string `gorm:"column:action_code;type:varchar(255);not null" json:"action_code"`
	ProjectID        uint   `gorm:"column:project_id;not null" json:"project_id"`
	ReleaseID        string `gorm:"column:release_id;type:varchar(255);not null" json:"release_id"`
	ReleaseHistoryID uint   `gorm:"column:release_history_id;not null" json:"release_history_id"`
	CreateTime       int    `gorm:"column:create_time;not null;default:0" json:"create_time"`
	ProjectName      string `gorm:"column:project_name;type:varchar(255);not null" json:"project_name"`
	ReleaseName      string `gorm:"column:release_name;type:varchar(255);not null" json:"release_name"`
}

// TableName specifies the table name for ActionLog
func (ActionLog) TableName() string {
	return "k8swan_action_log"
}
