package models

import (
	"fmt"
	"log"

	"github.com/privasea/motivus/config"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// InitDB initializes the database connection
func InitDB() error {
	dbConfig := config.AppConfig.Database
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbConfig.Username,
		dbConfig.Password,
		dbConfig.Host,
		dbConfig.Port,
		dbConfig.DBName,
	)

	var err error
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	log.Println("Connected to database successfully")

	// Auto migrate the schema
	err = DB.AutoMigrate(
		&Project{},
		&Release{},
		&ReleaseHistory{},
		&UserProject{},
		&ActionLog{},
		&ProjectTag{},
		&ReleasePlan{},
		&PlanItem{},
		&PlanItemRelease{},
		&DingdingUserID{},
		&Cluster{},
		&Role{},
		&Permission{},
		&RolePermission{},
		&User{},
		&UserRole{},
		&ProjectRole{},
	)
	if err != nil {
		return fmt.Errorf("failed to migrate database: %w", err)
	}

	log.Println("Database migration completed")
	return nil
}

// BaseModel contains common columns for all tables
type BaseModel struct {
	ID         uint `gorm:"primaryKey" json:"id"`
	CreateTime int  `gorm:"column:create_time;not null;default:0" json:"create_time"`
	UpdateTime int  `gorm:"column:update_time;not null;default:0" json:"update_time"`
	Deleted    bool `gorm:"column:deleted;default:false" json:"deleted"`
}
