package models

// Role represents a user role in the system
type Role struct {
	BaseModel
	Name        string `gorm:"column:name;type:varchar(50);uniqueIndex;not null" json:"name"`
	Description string `gorm:"column:description;type:text" json:"description"`
	IsSystem    bool   `gorm:"column:is_system;default:false" json:"is_system"` // System roles cannot be deleted
}

// TableName specifies the table name for Role
func (Role) TableName() string {
	return "roles"
}

// Permission represents a permission in the system
type Permission struct {
	BaseModel
	Name        string `gorm:"column:name;type:varchar(100);uniqueIndex;not null" json:"name"`
	Description string `gorm:"column:description;type:text" json:"description"`
	Resource    string `gorm:"column:resource;type:varchar(50);not null" json:"resource"` // e.g., "project", "release", "configmap"
	Action      string `gorm:"column:action;type:varchar(50);not null" json:"action"`     // e.g., "create", "read", "update", "delete"
}

// TableName specifies the table name for Permission
func (Permission) TableName() string {
	return "permissions"
}

// RolePermission represents the many-to-many relationship between roles and permissions
type RolePermission struct {
	ID           uint `gorm:"primaryKey" json:"id"`
	RoleID       uint `gorm:"column:role_id;not null;index:idx_role_permission" json:"role_id"`
	PermissionID uint `gorm:"column:permission_id;not null;index:idx_role_permission" json:"permission_id"`
}

// TableName specifies the table name for RolePermission
func (RolePermission) TableName() string {
	return "role_permissions"
}

// User represents a user in the system
type User struct {
	BaseModel
	Username  string `gorm:"column:username;type:varchar(50);uniqueIndex;not null" json:"username"`
	Password  string `gorm:"column:password;type:varchar(255);not null" json:"-"` // Password is not exposed in JSON
	Email     string `gorm:"column:email;type:varchar(100);uniqueIndex;not null" json:"email"`
	FullName  string `gorm:"column:full_name;type:varchar(100)" json:"full_name"`
	IsActive  bool   `gorm:"column:is_active;default:true" json:"is_active"`
	LastLogin int    `gorm:"column:last_login;default:0" json:"last_login"`
}

// TableName specifies the table name for User
func (User) TableName() string {
	return "users"
}

// UserRole represents the many-to-many relationship between users and roles
type UserRole struct {
	ID     uint `gorm:"primaryKey" json:"id"`
	UserID uint `gorm:"column:user_id;not null;index:idx_user_role" json:"user_id"`
	RoleID uint `gorm:"column:role_id;not null;index:idx_user_role" json:"role_id"`
}

// TableName specifies the table name for UserRole
func (UserRole) TableName() string {
	return "user_roles"
}

// ProjectRole represents a role specific to a project
type ProjectRole struct {
	BaseModel
	ProjectID    uint   `gorm:"column:project_id;not null;index:idx_project_user_role" json:"project_id"`
	UserID       uint   `gorm:"column:user_id;not null;index:idx_project_user_role" json:"user_id"`
	RoleID       uint   `gorm:"column:role_id;not null" json:"role_id"`
	GrantedByID  uint   `gorm:"column:granted_by_id;not null" json:"granted_by_id"` // User who granted this role
	GrantedByName string `gorm:"column:granted_by_name;type:varchar(50);not null" json:"granted_by_name"`
}

// TableName specifies the table name for ProjectRole
func (ProjectRole) TableName() string {
	return "project_roles"
}
