package models

// Cluster 表示一个Kubernetes集群的配置
type Cluster struct {
	BaseModel
	Name        string `gorm:"column:name;uniqueIndex;size:255;not null" json:"name"`
	Server      string `gorm:"column:server;size:255;not null" json:"server"`
	Description string `gorm:"column:description;size:255" json:"description"`
	Token       string `gorm:"column:token;size:2048" json:"-"`     // 不在JSON响应中返回敏感信息
	CAData      string `gorm:"column:ca_data;type:text" json:"-"`   // 不在JSON响应中返回敏感信息
	CAFile      string `gorm:"column:ca_file;size:255" json:"-"`    // 不在JSON响应中返回敏感信息
	Insecure    bool   `gorm:"column:insecure;default:false" json:"insecure"`
	IsDefault   bool   `gorm:"column:is_default;default:false" json:"is_default"`
	CreatedBy   string `gorm:"column:created_by;size:255;not null" json:"created_by"`
	UpdatedBy   string `gorm:"column:updated_by;size:255;not null" json:"updated_by"`
}

// TableName 指定表名
func (Cluster) TableName() string {
	return "k8s_clusters"
}
