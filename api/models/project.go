package models

// Project represents the k8swan_project table
type Project struct {
	BaseModel
	ProjectName     string `gorm:"column:project_name;type:varchar(255);uniqueIndex;not null" json:"project_name"`
	ProjectDescribe string `gorm:"column:project_describe;not null" json:"project_describe"`
	ChartRepoName   string `gorm:"column:chart_repo_name;not null" json:"chart_repo_name"`
	ChartRepoURL    string `gorm:"column:chart_repo_url;not null" json:"chart_repo_url"`
	ChartName       string `gorm:"column:chart_name;not null" json:"chart_name"`
	Creater         string `gorm:"column:creater;not null" json:"creater"`
}

// TableName specifies the table name for Project
func (Project) TableName() string {
	return "k8swan_project"
}

// ProjectTag represents the project_tag table
type ProjectTag struct {
	ID        uint   `gorm:"primaryKey" json:"id"`
	ProjectID uint   `gorm:"column:project_id;not null" json:"project_id"`
	Tag       string `gorm:"column:tag;type:varchar(255);not null" json:"tag"`
}

// TableName specifies the table name for ProjectTag
func (ProjectTag) TableName() string {
	return "project_tag"
}

// UserProject represents the k8swan_user_project table
type UserProject struct {
	ID         uint   `gorm:"primaryKey" json:"id"`
	Username   string `gorm:"column:username;not null;index" json:"username"`
	ProjectID  uint   `gorm:"column:project_id;not null" json:"project_id"`
	UserRole   string `gorm:"column:user_role;not null" json:"user_role"`
	CreateTime int    `gorm:"column:create_time;not null;default:0" json:"create_time"`
}

// TableName specifies the table name for UserProject
func (UserProject) TableName() string {
	return "k8swan_user_project"
}
