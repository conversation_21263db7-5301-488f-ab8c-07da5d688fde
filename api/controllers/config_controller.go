package controllers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/privasea/motivus/services"
)

// ConfigController handles configuration-related API endpoints
type ConfigController struct {
	configService *services.ConfigService
}

// NewConfigController creates a new ConfigController
func NewConfigController() *ConfigController {
	return &ConfigController{
		configService: services.NewConfigService(),
	}
}

// getConfigServiceForCluster returns a ConfigService for the specified cluster
func (cc *ConfigController) getConfigServiceForCluster(c *gin.Context) *services.ConfigService {
	// Check if cluster is specified in query parameters
	cluster := c.Query("cluster")
	if cluster == "" {
		// Use the default ConfigService
		return cc.configService
	}

	// Log the cluster name for debugging
	fmt.Printf("Using cluster: %s\n", cluster)

	// Create a new ConfigService for the specified cluster
	configService := services.NewConfigServiceForCluster(cluster)
	return configService
}

// CreateSecret creates a new Secret
func (cc *ConfigController) CreateSecret(c *gin.Context) {
	var secretRequest struct {
		Name      string            `json:"name" binding:"required"`
		Namespace string            `json:"namespace" binding:"required"`
		Data      map[string]string `json:"data" binding:"required"`
		Labels    map[string]string `json:"labels"`
	}

	if err := c.ShouldBindJSON(&secretRequest); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	// Get ConfigService for the specified cluster
	configService := cc.getConfigServiceForCluster(c)

	secret, err := configService.CreateSecret(
		secretRequest.Name,
		secretRequest.Namespace,
		secretRequest.Data,
		secretRequest.Labels,
		username.(string),
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"data": secret})
}

// GetSecret gets a Secret by name and namespace
func (cc *ConfigController) GetSecret(c *gin.Context) {
	name := c.Param("name")
	namespace := c.Query("namespace")
	if namespace == "" {
		namespace = "default"
	}

	// Get ConfigService for the specified cluster
	configService := cc.getConfigServiceForCluster(c)

	secret, err := configService.GetSecret(name, namespace)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "secret not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": secret})
}

// UpdateSecret updates a Secret
func (cc *ConfigController) UpdateSecret(c *gin.Context) {
	name := c.Param("name")
	namespace := c.Query("namespace")
	if namespace == "" {
		namespace = "default"
	}

	var secretRequest struct {
		Data   map[string]string `json:"data" binding:"required"`
		Labels map[string]string `json:"labels"`
	}

	if err := c.ShouldBindJSON(&secretRequest); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	// Get ConfigService for the specified cluster
	configService := cc.getConfigServiceForCluster(c)

	secret, err := configService.UpdateSecret(
		name,
		namespace,
		secretRequest.Data,
		secretRequest.Labels,
		username.(string),
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": secret})
}

// DeleteSecret deletes a Secret
func (cc *ConfigController) DeleteSecret(c *gin.Context) {
	name := c.Param("name")
	namespace := c.Query("namespace")
	if namespace == "" {
		namespace = "default"
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	// Get ConfigService for the specified cluster
	configService := cc.getConfigServiceForCluster(c)

	if err := configService.DeleteSecret(name, namespace, username.(string)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "secret deleted successfully"})
}

// ListSecrets lists all Secrets in a namespace
func (cc *ConfigController) ListSecrets(c *gin.Context) {
	namespace := c.Query("namespace")
	if namespace == "" {
		namespace = "default"
	}

	// Get ConfigService for the specified cluster
	configService := cc.getConfigServiceForCluster(c)

	secrets, err := configService.ListSecrets(namespace)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": secrets})
}

// CreateConfigMap creates a new ConfigMap
func (cc *ConfigController) CreateConfigMap(c *gin.Context) {
	var configMapRequest struct {
		Name      string            `json:"name" binding:"required"`
		Namespace string            `json:"namespace" binding:"required"`
		Data      map[string]string `json:"data" binding:"required"`
		Labels    map[string]string `json:"labels"`
	}

	if err := c.ShouldBindJSON(&configMapRequest); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	// Get ConfigService for the specified cluster
	configService := cc.getConfigServiceForCluster(c)

	configMap, err := configService.CreateConfigMap(
		configMapRequest.Name,
		configMapRequest.Namespace,
		configMapRequest.Data,
		configMapRequest.Labels,
		username.(string),
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"data": configMap})
}

// GetConfigMap gets a ConfigMap by name and namespace
func (cc *ConfigController) GetConfigMap(c *gin.Context) {
	name := c.Param("name")
	namespace := c.Query("namespace")
	if namespace == "" {
		namespace = "default"
	}

	// Get ConfigService for the specified cluster
	configService := cc.getConfigServiceForCluster(c)

	configMap, err := configService.GetConfigMap(name, namespace)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "configmap not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": configMap})
}

// UpdateConfigMap updates a ConfigMap
func (cc *ConfigController) UpdateConfigMap(c *gin.Context) {
	name := c.Param("name")
	namespace := c.Query("namespace")
	if namespace == "" {
		namespace = "default"
	}

	var configMapRequest struct {
		Data   map[string]string `json:"data" binding:"required"`
		Labels map[string]string `json:"labels"`
	}

	if err := c.ShouldBindJSON(&configMapRequest); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	// Get ConfigService for the specified cluster
	configService := cc.getConfigServiceForCluster(c)

	configMap, err := configService.UpdateConfigMap(
		name,
		namespace,
		configMapRequest.Data,
		configMapRequest.Labels,
		username.(string),
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": configMap})
}

// DeleteConfigMap deletes a ConfigMap
func (cc *ConfigController) DeleteConfigMap(c *gin.Context) {
	name := c.Param("name")
	namespace := c.Query("namespace")
	if namespace == "" {
		namespace = "default"
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	// Get ConfigService for the specified cluster
	configService := cc.getConfigServiceForCluster(c)

	if err := configService.DeleteConfigMap(name, namespace, username.(string)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "configmap deleted successfully"})
}

// ListConfigMaps lists all ConfigMaps in a namespace
func (cc *ConfigController) ListConfigMaps(c *gin.Context) {
	namespace := c.Query("namespace")
	if namespace == "" {
		namespace = "default"
	}

	// Get ConfigService for the specified cluster
	configService := cc.getConfigServiceForCluster(c)

	configMaps, err := configService.ListConfigMaps(namespace)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": configMaps})
}

// GetReleaseValues gets the values for a release
func (cc *ConfigController) GetReleaseValues(c *gin.Context) {
	releaseID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid release ID"})
		return
	}

	values, err := cc.configService.GetReleaseValues(uint(releaseID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": values})
}

// UpdateReleaseValues updates the values for a release
func (cc *ConfigController) UpdateReleaseValues(c *gin.Context) {
	releaseID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid release ID"})
		return
	}

	var valuesRequest struct {
		Values string `json:"values" binding:"required"`
	}

	if err := c.ShouldBindJSON(&valuesRequest); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	if err := cc.configService.UpdateReleaseValues(uint(releaseID), valuesRequest.Values, username.(string)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "release values updated successfully"})
}
