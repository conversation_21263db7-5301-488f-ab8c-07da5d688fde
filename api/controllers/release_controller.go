package controllers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/privasea/motivus/models"
	"github.com/privasea/motivus/services"
)

// ReleaseController handles release-related API endpoints
type ReleaseController struct {
	releaseService *services.ReleaseService
}

// NewReleaseController creates a new ReleaseController
func NewReleaseController() *ReleaseController {
	return &ReleaseController{
		releaseService: services.NewReleaseService(),
	}
}

// CreateRelease creates a new release
func (rc *ReleaseController) CreateRelease(c *gin.Context) {
	var release models.Release
	if err := c.ShouldBindJSON(&release); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set creator from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}
	release.Creater = username.(string)

	if err := rc.releaseService.CreateRelease(&release); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"data": release})
}

// GetRelease gets a release by ID
func (rc *ReleaseController) GetRelease(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid release ID"})
		return
	}

	release, err := rc.releaseService.GetReleaseByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "release not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": release})
}

// UpdateRelease updates a release
func (rc *ReleaseController) UpdateRelease(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid release ID"})
		return
	}

	var release models.Release
	if err := c.ShouldBindJSON(&release); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	release.ID = uint(id)
	if err := rc.releaseService.UpdateRelease(&release); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": release})
}

// DeleteRelease deletes a release
func (rc *ReleaseController) DeleteRelease(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid release ID"})
		return
	}

	if err := rc.releaseService.DeleteRelease(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "release deleted successfully"})
}

// ListReleases lists all releases
func (rc *ReleaseController) ListReleases(c *gin.Context) {
	projectID := c.Query("project_id")
	
	var releases []models.Release
	var err error
	
	if projectID != "" {
		pid, err := strconv.ParseUint(projectID, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid project ID"})
			return
		}
		releases, err = rc.releaseService.GetReleasesByProjectID(uint(pid))
	} else {
		releases, err = rc.releaseService.ListReleases()
	}
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": releases})
}

// DeployRelease deploys a release
func (rc *ReleaseController) DeployRelease(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid release ID"})
		return
	}

	var deployRequest struct {
		ChartVersion string `json:"chart_version" binding:"required"`
		ImageTag     string `json:"image_tag" binding:"required"`
		ChangeLog    string `json:"change_log"`
	}
	
	if err := c.ShouldBindJSON(&deployRequest); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	releaseHistory, err := rc.releaseService.DeployRelease(
		uint(id), 
		deployRequest.ChartVersion, 
		deployRequest.ImageTag, 
		deployRequest.ChangeLog, 
		username.(string),
	)
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": releaseHistory})
}

// RollbackRelease rolls back a release to a previous version
func (rc *ReleaseController) RollbackRelease(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid release ID"})
		return
	}

	historyID, err := strconv.ParseUint(c.Param("historyId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid history ID"})
		return
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	releaseHistory, err := rc.releaseService.RollbackRelease(uint(id), uint(historyID), username.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": releaseHistory})
}

// GetReleaseHistory gets the deployment history of a release
func (rc *ReleaseController) GetReleaseHistory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid release ID"})
		return
	}

	history, err := rc.releaseService.GetReleaseHistory(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": history})
}
