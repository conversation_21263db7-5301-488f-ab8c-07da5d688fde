package controllers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/privasea/motivus/models"
	"github.com/privasea/motivus/services"
)

// PlanController handles release plan-related API endpoints
type PlanController struct {
	planService *services.PlanService
}

// NewPlanController creates a new PlanController
func NewPlanController() *PlanController {
	return &PlanController{
		planService: services.NewPlanService(),
	}
}

// CreatePlan creates a new release plan
func (pc *PlanController) CreatePlan(c *gin.Context) {
	var plan models.ReleasePlan
	if err := c.ShouldBindJSON(&plan); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set creator from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}
	plan.Creater = username.(string)
	plan.Updater = username.(string)

	if err := pc.planService.CreatePlan(&plan); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"data": plan})
}

// GetPlan gets a plan by ID
func (pc *PlanController) GetPlan(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid plan ID"})
		return
	}

	plan, err := pc.planService.GetPlanByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "plan not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": plan})
}

// UpdatePlan updates a plan
func (pc *PlanController) UpdatePlan(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid plan ID"})
		return
	}

	var plan models.ReleasePlan
	if err := c.ShouldBindJSON(&plan); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set updater from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}
	plan.Updater = username.(string)

	plan.ID = uint(id)
	if err := pc.planService.UpdatePlan(&plan); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": plan})
}

// DeletePlan deletes a plan
func (pc *PlanController) DeletePlan(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid plan ID"})
		return
	}

	if err := pc.planService.DeletePlan(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "plan deleted successfully"})
}

// ListPlans lists all plans
func (pc *PlanController) ListPlans(c *gin.Context) {
	plans, err := pc.planService.ListPlans()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": plans})
}

// AddPlanItem adds an item to a plan
func (pc *PlanController) AddPlanItem(c *gin.Context) {
	planID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid plan ID"})
		return
	}

	var item models.PlanItem
	if err := c.ShouldBindJSON(&item); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set creator and updater from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}
	item.Creater = username.(string)
	item.Updater = username.(string)
	item.PlanID = uint(planID)

	if err := pc.planService.AddPlanItem(&item); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"data": item})
}

// UpdatePlanItem updates a plan item
func (pc *PlanController) UpdatePlanItem(c *gin.Context) {
	planID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid plan ID"})
		return
	}

	itemID, err := strconv.ParseUint(c.Param("itemId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid item ID"})
		return
	}

	var item models.PlanItem
	if err := c.ShouldBindJSON(&item); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set updater from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}
	item.Updater = username.(string)
	item.PlanID = uint(planID)
	item.ID = uint(itemID)

	if err := pc.planService.UpdatePlanItem(&item); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": item})
}

// DeletePlanItem deletes a plan item
func (pc *PlanController) DeletePlanItem(c *gin.Context) {
	itemID, err := strconv.ParseUint(c.Param("itemId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid item ID"})
		return
	}

	if err := pc.planService.DeletePlanItem(uint(itemID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "plan item deleted successfully"})
}

// GetPlanItems gets all items for a plan
func (pc *PlanController) GetPlanItems(c *gin.Context) {
	planID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid plan ID"})
		return
	}

	items, err := pc.planService.GetPlanItems(uint(planID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": items})
}

// AddPlanItemRelease adds a release to a plan item
func (pc *PlanController) AddPlanItemRelease(c *gin.Context) {
	itemID, err := strconv.ParseUint(c.Param("itemId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid item ID"})
		return
	}

	var itemRelease models.PlanItemRelease
	if err := c.ShouldBindJSON(&itemRelease); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	itemRelease.PlanItemID = uint(itemID)

	if err := pc.planService.AddPlanItemRelease(&itemRelease); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"data": itemRelease})
}

// UpdatePlanItemRelease updates a plan item release
func (pc *PlanController) UpdatePlanItemRelease(c *gin.Context) {
	releaseID, err := strconv.ParseUint(c.Param("releaseId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid release ID"})
		return
	}

	var itemRelease models.PlanItemRelease
	if err := c.ShouldBindJSON(&itemRelease); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	itemRelease.ID = uint(releaseID)

	if err := pc.planService.UpdatePlanItemRelease(&itemRelease); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": itemRelease})
}

// DeletePlanItemRelease deletes a plan item release
func (pc *PlanController) DeletePlanItemRelease(c *gin.Context) {
	releaseID, err := strconv.ParseUint(c.Param("releaseId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid release ID"})
		return
	}

	if err := pc.planService.DeletePlanItemRelease(uint(releaseID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "plan item release deleted successfully"})
}

// GetPlanItemReleases gets all releases for a plan item
func (pc *PlanController) GetPlanItemReleases(c *gin.Context) {
	itemID, err := strconv.ParseUint(c.Param("itemId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid item ID"})
		return
	}

	releases, err := pc.planService.GetPlanItemReleases(uint(itemID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": releases})
}

// ExecutePlan executes a release plan
func (pc *PlanController) ExecutePlan(c *gin.Context) {
	planID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid plan ID"})
		return
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	if err := pc.planService.ExecutePlan(uint(planID), username.(string)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "plan execution started"})
}

// SchedulePlan schedules a plan for future execution
func (pc *PlanController) SchedulePlan(c *gin.Context) {
	planID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid plan ID"})
		return
	}

	var scheduleRequest struct {
		ScheduleTime int64 `json:"schedule_time" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&scheduleRequest); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	// Convert Unix timestamp to time.Time
	scheduleTime := time.Unix(scheduleRequest.ScheduleTime, 0)

	if err := pc.planService.SchedulePlan(uint(planID), scheduleTime, username.(string)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "plan scheduled successfully"})
}
