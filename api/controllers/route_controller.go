package controllers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/privasea/motivus/pkg/apisix"
	"github.com/privasea/motivus/services"
)

// RouteController handles API route management endpoints
type RouteController struct {
	routeService *services.RouteService
}

// NewRouteController creates a new RouteController
func NewRouteController() *RouteController {
	return &RouteController{
		routeService: services.NewRouteService(),
	}
}

// CreateRoute creates a new API route
func (rc *RouteController) CreateRoute(c *gin.Context) {
	var route apisix.Route
	if err := c.ShouldBindJSON(&route); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	routeID, err := rc.routeService.CreateRoute(&route, username.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"data": gin.H{"id": routeID}})
}

// GetRoute gets a route by ID
func (rc *RouteController) GetRoute(c *gin.Context) {
	id := c.Param("id")

	route, err := rc.routeService.GetRoute(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "route not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": route})
}

// UpdateRoute updates a route
func (rc *RouteController) UpdateRoute(c *gin.Context) {
	id := c.Param("id")

	var route apisix.Route
	if err := c.ShouldBindJSON(&route); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	route.ID = id
	if err := rc.routeService.UpdateRoute(&route, username.(string)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "route updated successfully"})
}

// DeleteRoute deletes a route
func (rc *RouteController) DeleteRoute(c *gin.Context) {
	id := c.Param("id")

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	if err := rc.routeService.DeleteRoute(id, username.(string)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "route deleted successfully"})
}

// ListRoutes lists all routes
func (rc *RouteController) ListRoutes(c *gin.Context) {
	routes, err := rc.routeService.ListRoutes()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": routes})
}

// CreateUpstream creates a new upstream
func (rc *RouteController) CreateUpstream(c *gin.Context) {
	var upstream apisix.Upstream
	if err := c.ShouldBindJSON(&upstream); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	upstreamID, err := rc.routeService.CreateUpstream(&upstream, username.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"data": gin.H{"id": upstreamID}})
}

// GetUpstream gets an upstream by ID
func (rc *RouteController) GetUpstream(c *gin.Context) {
	id := c.Param("id")

	upstream, err := rc.routeService.GetUpstream(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "upstream not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": upstream})
}

// UpdateUpstream updates an upstream
func (rc *RouteController) UpdateUpstream(c *gin.Context) {
	id := c.Param("id")

	var upstream apisix.Upstream
	if err := c.ShouldBindJSON(&upstream); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	upstream.ID = id
	if err := rc.routeService.UpdateUpstream(&upstream, username.(string)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "upstream updated successfully"})
}

// DeleteUpstream deletes an upstream
func (rc *RouteController) DeleteUpstream(c *gin.Context) {
	id := c.Param("id")

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "username not found in token"})
		return
	}

	if err := rc.routeService.DeleteUpstream(id, username.(string)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "upstream deleted successfully"})
}

// ListUpstreams lists all upstreams
func (rc *RouteController) ListUpstreams(c *gin.Context) {
	upstreams, err := rc.routeService.ListUpstreams()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": upstreams})
}
