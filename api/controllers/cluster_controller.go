package controllers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/privasea/motivus/services"
)

// ClusterController handles cluster management API endpoints
type ClusterController struct {
	clusterService *services.ClusterService
}

// NewClusterController creates a new ClusterController
func NewClusterController() *ClusterController {
	return &ClusterController{
		clusterService: services.NewClusterService(),
	}
}

// ListClusters lists all available clusters
func (cc *ClusterController) ListClusters(c *gin.Context) {
	clusters, err := cc.clusterService.ListClusters()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": clusters})
}

// GetCluster gets a specific cluster by name
func (cc *ClusterController) GetCluster(c *gin.Context) {
	name := c.Param("name")
	cluster, err := cc.clusterService.GetCluster(name)
	if err != nil {
		c.J<PERSON>(http.StatusNotFound, gin.H{"error": err.<PERSON>rror()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": cluster})
}

// GetClusterDetails gets detailed information about a specific cluster including sensitive data
func (cc *ClusterController) GetClusterDetails(c *gin.Context) {
	name := c.Param("name")

	clusterDetails, err := cc.clusterService.GetClusterDetails(name)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": clusterDetails})
}

// AddCluster adds a new cluster
func (cc *ClusterController) AddCluster(c *gin.Context) {
	var request struct {
		Name        string `json:"name" binding:"required"`
		Server      string `json:"server" binding:"required"`
		Token       string `json:"token"`
		CAData      string `json:"ca_data"`
		Description string `json:"description"`
		IsDefault   bool   `json:"is_default"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		username = "system" // Default to system if no username found
	}

	err := cc.clusterService.AddCluster(request.Name, request.Server, request.Token, request.CAData, request.IsDefault, username.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "Cluster added successfully"})
}

// UpdateCluster updates an existing cluster
func (cc *ClusterController) UpdateCluster(c *gin.Context) {
	name := c.Param("name")

	var request struct {
		Server      string `json:"server"`
		Token       string `json:"token"`
		CAData      string `json:"ca_data"`
		Description string `json:"description"`
		IsDefault   bool   `json:"is_default"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get username from JWT claims
	username, exists := c.Get("username")
	if !exists {
		username = "system" // Default to system if no username found
	}

	err := cc.clusterService.UpdateCluster(name, request.Server, request.Token, request.CAData, request.IsDefault, username.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Cluster updated successfully"})
}

// DeleteCluster deletes a cluster
func (cc *ClusterController) DeleteCluster(c *gin.Context) {
	name := c.Param("name")

	err := cc.clusterService.DeleteCluster(name)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Cluster deleted successfully"})
}

// SetDefaultCluster sets a cluster as the default
func (cc *ClusterController) SetDefaultCluster(c *gin.Context) {
	name := c.Param("name")

	err := cc.clusterService.SetDefaultCluster(name)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Default cluster set successfully"})
}

// ListNamespaces lists all namespaces in a cluster
func (cc *ClusterController) ListNamespaces(c *gin.Context) {
	// Get cluster name from query parameter
	clusterName := c.Query("cluster")
	if clusterName == "" {
		// Use default cluster if not specified
		clusterName = cc.clusterService.GetDefaultCluster()
		if clusterName == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "no cluster specified and no default cluster found"})
			return
		}
	}

	// Get namespaces from the cluster
	namespaces, err := cc.clusterService.ListNamespaces(clusterName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": namespaces})
}
