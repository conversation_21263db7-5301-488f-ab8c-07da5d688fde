package controllers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/privasea/motivus/services"
)

// RoleController handles role and permission management
type RoleController struct {
	roleService services.RoleService
}

// NewRoleController creates a new RoleController
func NewRoleController() *RoleController {
	return &RoleController{
		roleService: services.RoleService{},
	}
}

// GetAllRoles returns all roles in the system
func (c *RoleController) GetAllRoles(ctx *gin.Context) {
	roles, err := c.roleService.GetAllRoles()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": roles})
}

// GetRolePermissions returns all permissions assigned to a role
func (c *RoleController) GetRolePermissions(ctx *gin.Context) {
	roleName := ctx.Param("name")
	permissions, err := c.roleService.GetRolePermissions(roleName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": permissions})
}

// GetUserRoles returns all roles assigned to a user
func (c *RoleController) GetUserRoles(ctx *gin.Context) {
	username := ctx.Param("username")
	roles, err := c.roleService.GetUserRoles(username)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": roles})
}

// GetUserProjectRoles returns all roles assigned to a user for a specific project
func (c *RoleController) GetUserProjectRoles(ctx *gin.Context) {
	username := ctx.Param("username")
	projectIDStr := ctx.Param("projectId")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	roles, err := c.roleService.GetUserProjectRoles(username, uint(projectID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": roles})
}

// AssignRoleToUser assigns a role to a user
func (c *RoleController) AssignRoleToUser(ctx *gin.Context) {
	var request struct {
		Username string `json:"username" binding:"required"`
		RoleName string `json:"role_name" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查当前用户是否有权限分配角色
	currentUser := ctx.GetString("username")
	hasPermission, err := c.roleService.HasPermission(currentUser, "role_assign")
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if !hasPermission {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "You don't have permission to assign roles"})
		return
	}

	err = c.roleService.AssignRoleToUser(request.Username, request.RoleName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Role assigned successfully"})
}

// AssignRoleToUserForProject assigns a role to a user for a specific project
func (c *RoleController) AssignRoleToUserForProject(ctx *gin.Context) {
	var request struct {
		Username  string `json:"username" binding:"required"`
		RoleName  string `json:"role_name" binding:"required"`
		ProjectID uint   `json:"project_id" binding:"required"`
	}

	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查当前用户是否有权限分配项目角色
	currentUser := ctx.GetString("username")
	hasPermission, err := c.roleService.HasProjectPermission(currentUser, request.ProjectID, "project_manage_members")
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if !hasPermission {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "You don't have permission to manage project members"})
		return
	}

	err = c.roleService.AssignRoleToUserForProject(request.Username, request.RoleName, request.ProjectID, currentUser)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Project role assigned successfully"})
}

// CheckPermission checks if the current user has a specific permission
func (c *RoleController) CheckPermission(ctx *gin.Context) {
	permissionName := ctx.Param("name")
	username := ctx.GetString("username")

	hasPermission, err := c.roleService.HasPermission(username, permissionName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"has_permission": hasPermission})
}

// CheckProjectPermission checks if the current user has a specific permission for a project
func (c *RoleController) CheckProjectPermission(ctx *gin.Context) {
	permissionName := ctx.Param("name")
	projectIDStr := ctx.Param("projectId")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid project ID"})
		return
	}

	username := ctx.GetString("username")

	hasPermission, err := c.roleService.HasProjectPermission(username, uint(projectID), permissionName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"has_permission": hasPermission})
}
