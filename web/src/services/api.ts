import axios, { AxiosRequestConfig } from 'axios';
import {
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  Project,
  ProjectTag,
  Release,
  ReleaseHistory,
  ReleasePlan,
  PlanItem,
  PlanItemRelease,
  Secret,
  ConfigMap,
  Route,
  Upstream,
  ApiResponse,
  PaginatedResponse
} from '../types';

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8080/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器，添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器，处理错误
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response && error.response.status === 401) {
      // 未授权，清除token并重定向到登录页
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 认证相关API
export const authApi = {
  login: (data: LoginRequest) =>
    api.post<AuthResponse>('/auth/login', data),

  register: (data: RegisterRequest) =>
    api.post<AuthResponse>('/auth/register', data),

  getProfile: () =>
    api.get('/auth/profile'),

  updateProfile: (data: Partial<RegisterRequest>) =>
    api.put('/auth/profile', data),
};

// 项目相关API
export const projectApi = {
  getProjects: () =>
    api.get<ApiResponse<Project[]>>('/projects'),

  getProject: (id: number) =>
    api.get<Project>(`/projects/${id}`),

  createProject: (data: Partial<Project>) =>
    api.post<Project>('/projects', data),

  updateProject: (id: number, data: Partial<Project>) =>
    api.put<Project>(`/projects/${id}`, data),

  deleteProject: (id: number) =>
    api.delete(`/projects/${id}`),

  getProjectTags: (projectId: number) =>
    api.get<ApiResponse<ProjectTag[]>>(`/projects/${projectId}/tags`),

  addProjectTag: (projectId: number, tag: string) =>
    api.post<ProjectTag>(`/projects/${projectId}/tags`, { tag }),
};

// 发布相关API
export const releaseApi = {
  getReleases: (projectId?: number) => {
    const params: Record<string, any> = {};
    if (projectId) {
      params.project_id = projectId;
    }
    return api.get<ApiResponse<Release[]>>('/releases', { params });
  },

  getRelease: (id: number) =>
    api.get<Release>(`/releases/${id}`),

  createRelease: (data: Partial<Release>) =>
    api.post<Release>('/releases', data),

  updateRelease: (id: number, data: Partial<Release>) =>
    api.put<Release>(`/releases/${id}`, data),

  deleteRelease: (id: number) =>
    api.delete(`/releases/${id}`),

  deployRelease: (id: number, data: { change_log: string }) =>
    api.post(`/releases/${id}/deploy`, data),

  getReleaseHistory: (releaseId: number) =>
    api.get<ApiResponse<ReleaseHistory[]>>(`/releases/${releaseId}/history`),
};

// 计划相关API
export const planApi = {
  getPlans: () =>
    api.get<ApiResponse<ReleasePlan[]>>('/plans'),

  getPlan: (id: number) =>
    api.get<ReleasePlan>(`/plans/${id}`),

  createPlan: (data: Partial<ReleasePlan>) =>
    api.post<ReleasePlan>('/plans', data),

  updatePlan: (id: number, data: Partial<ReleasePlan>) =>
    api.put<ReleasePlan>(`/plans/${id}`, data),

  deletePlan: (id: number) =>
    api.delete(`/plans/${id}`),

  getPlanItems: (planId: number) =>
    api.get<ApiResponse<PlanItem[]>>(`/plans/${planId}/items`),

  createPlanItem: (planId: number, data: Partial<PlanItem>) =>
    api.post<PlanItem>(`/plans/${planId}/items`, data),

  updatePlanItem: (planId: number, itemId: number, data: Partial<PlanItem>) =>
    api.put<PlanItem>(`/plans/${planId}/items/${itemId}`, data),

  deletePlanItem: (planId: number, itemId: number) =>
    api.delete(`/plans/${planId}/items/${itemId}`),

  getPlanItemReleases: (itemId: number) =>
    api.get<ApiResponse<PlanItemRelease[]>>(`/plans/items/${itemId}/releases`),

  createPlanItemRelease: (itemId: number, data: Partial<PlanItemRelease>) =>
    api.post<PlanItemRelease>(`/plans/items/${itemId}/releases`, data),
};

// 配置相关API
export const configApi = {
  getSecrets: (namespace?: string, cluster?: string) => {
    const params: Record<string, any> = {};
    if (namespace) {
      params.namespace = namespace;
    }
    if (cluster) {
      params.cluster = cluster;
    }
    return api.get<ApiResponse<Secret[]>>('/configs/secrets', { params });
  },

  createSecret: (data: Secret, cluster?: string) => {
    const params: Record<string, any> = {};
    if (cluster) {
      params.cluster = cluster;
    }
    return api.post<Secret>('/configs/secrets', data, { params });
  },

  updateSecret: (name: string, namespace: string, data: Partial<Secret>, cluster?: string) => {
    const params: Record<string, any> = { namespace };
    if (cluster) {
      params.cluster = cluster;
    }
    return api.put<Secret>(`/configs/secrets/${name}`, data, { params });
  },

  deleteSecret: (name: string, namespace: string, cluster?: string) => {
    const params: Record<string, any> = { namespace };
    if (cluster) {
      params.cluster = cluster;
    }
    return api.delete(`/configs/secrets/${name}`, { params });
  },

  getConfigMaps: (namespace?: string, cluster?: string) => {
    const params: Record<string, any> = {};
    if (namespace) {
      params.namespace = namespace;
    }
    if (cluster) {
      params.cluster = cluster;
    }
    return api.get<ApiResponse<ConfigMap[]>>('/configs/configmaps', { params });
  },

  createConfigMap: (data: ConfigMap, cluster?: string) => {
    const params: Record<string, any> = {};
    if (cluster) {
      params.cluster = cluster;
    }
    return api.post<ConfigMap>('/configs/configmaps', data, { params });
  },

  updateConfigMap: (name: string, namespace: string, data: Partial<ConfigMap>, cluster?: string) => {
    const params: Record<string, any> = { namespace };
    if (cluster) {
      params.cluster = cluster;
    }
    return api.put<ConfigMap>(`/configs/configmaps/${name}`, data, { params });
  },

  deleteConfigMap: (name: string, namespace: string, cluster?: string) => {
    const params: Record<string, any> = { namespace };
    if (cluster) {
      params.cluster = cluster;
    }
    return api.delete(`/configs/configmaps/${name}`, { params });
  },

  getReleaseValues: (releaseId: number) =>
    api.get<string>(`/configs/releases/${releaseId}/values`),
};

// 路由相关API
export const routeApi = {
  getRoutes: () =>
    api.get<ApiResponse<Route[]>>('/routes'),

  getRoute: (id: string) =>
    api.get<Route>(`/routes/${id}`),

  createRoute: (data: Partial<Route>) =>
    api.post<Route>('/routes', data),

  updateRoute: (id: string, data: Partial<Route>) =>
    api.put<Route>(`/routes/${id}`, data),

  deleteRoute: (id: string) =>
    api.delete(`/routes/${id}`),

  getUpstreams: () =>
    api.get<ApiResponse<Upstream[]>>('/routes/upstreams'),

  getUpstream: (id: string) =>
    api.get<Upstream>(`/routes/upstreams/${id}`),

  createUpstream: (data: Partial<Upstream>) =>
    api.post<Upstream>('/routes/upstreams', data),

  updateUpstream: (id: string, data: Partial<Upstream>) =>
    api.put<Upstream>(`/routes/upstreams/${id}`, data),

  deleteUpstream: (id: string) =>
    api.delete(`/routes/upstreams/${id}`),
};

// 集群相关API
export const clusterApi = {
  getClusters: () =>
    api.get<ApiResponse<any[]>>('/clusters'),

  getCluster: (name: string) =>
    api.get<any>(`/clusters/${name}`),

  getClusterDetails: (name: string) =>
    api.get<any>(`/clusters/${name}/details`),

  createCluster: (data: any) =>
    api.post<any>('/clusters', data),

  updateCluster: (name: string, data: any) =>
    api.put<any>(`/clusters/${name}`, data),

  deleteCluster: (name: string) =>
    api.delete(`/clusters/${name}`),

  setDefaultCluster: (name: string) =>
    api.post(`/clusters/${name}/default`),

  // 获取命名空间列表
  getNamespaces: (cluster?: string) => {
    const params: Record<string, any> = {};
    if (cluster) {
      params.cluster = cluster;
    }
    return api.get<ApiResponse<string[]>>('/namespaces', { params });
  },
};

// 角色和权限相关API
export const roleApi = {
  // 获取所有角色
  getRoles: () =>
    api.get<ApiResponse<any[]>>('/roles'),

  // 获取角色权限
  getRolePermissions: (roleName: string) =>
    api.get<ApiResponse<any[]>>(`/roles/${roleName}/permissions`),

  // 获取用户角色
  getUserRoles: (username: string) =>
    api.get<ApiResponse<any[]>>(`/roles/users/${username}`),

  // 获取用户项目角色
  getUserProjectRoles: (username: string, projectId: number) =>
    api.get<ApiResponse<any[]>>(`/roles/users/${username}/projects/${projectId}`),

  // 分配角色
  assignRole: (username: string, roleName: string) =>
    api.post('/roles/assign', { username, role_name: roleName }),

  // 分配项目角色
  assignProjectRole: (username: string, roleName: string, projectId: number) =>
    api.post('/roles/projects/assign', { username, role_name: roleName, project_id: projectId }),

  // 检查权限
  checkPermission: (permissionName: string) =>
    api.get<{ has_permission: boolean }>(`/roles/permissions/${permissionName}/check`),

  // 检查项目权限
  checkProjectPermission: (permissionName: string, projectId: number) =>
    api.get<{ has_permission: boolean }>(`/roles/permissions/${permissionName}/projects/${projectId}/check`),
};

export default api;
