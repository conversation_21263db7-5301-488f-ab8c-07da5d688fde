import React, { useEffect, useState } from 'react';
import { Table, Button, Modal, Form, Input, Switch, message, Space } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, CheckCircleOutlined } from '@ant-design/icons';
import AppLayout from '../components/AppLayout';
import { clusterApi } from '../services/api';

interface Cluster {
  name: string;
  server: string;
  status: string;
  version?: string;
  node_count?: number;
  is_default: boolean;
}

const Clusters: React.FC = () => {
  const [clusters, setClusters] = useState<Cluster[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [currentCluster, setCurrentCluster] = useState<Cluster | null>(null);
  const [form] = Form.useForm();

  const fetchClusters = async () => {
    setLoading(true);
    try {
      const response = await clusterApi.getClusters();
      setClusters(response.data.data || []);
    } catch (error: any) {
      console.error('Failed to fetch clusters:', error);
      message.error('获取集群列表失败: ' + (error.response?.data?.error || error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClusters();
  }, []);

  const handleAddOrEditCluster = (cluster?: Cluster) => {
    setCurrentCluster(cluster || null);
    form.resetFields();
    if (cluster) {
      form.setFieldsValue({
        name: cluster.name,
        server: cluster.server,
        is_default: cluster.is_default,
      });
    }
    setModalVisible(true);
  };

  const handleDeleteCluster = async (name: string) => {
    try {
      await clusterApi.deleteCluster(name);
      message.success('集群删除成功');
      fetchClusters();
    } catch (error: any) {
      console.error('Failed to delete cluster:', error);
      message.error('集群删除失败: ' + (error.response?.data?.error || error.message || '未知错误'));
    }
  };

  const handleSetDefaultCluster = async (name: string) => {
    try {
      await clusterApi.setDefaultCluster(name);
      message.success('默认集群设置成功');
      fetchClusters();
    } catch (error: any) {
      console.error('Failed to set default cluster:', error);
      message.error('设置默认集群失败: ' + (error.response?.data?.error || error.message || '未知错误'));
    }
  };

  const handleModalSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (currentCluster) {
        await clusterApi.updateCluster(currentCluster.name, values);
        message.success('集群更新成功');
      } else {
        await clusterApi.createCluster(values);
        message.success('集群创建成功');
      }

      setModalVisible(false);
      fetchClusters();
    } catch (error: any) {
      console.error('Form submission failed:', error);
      message.error('提交失败: ' + (error.response?.data?.error || error.message || '未知错误'));
    }
  };

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '服务器地址',
      dataIndex: 'server',
      key: 'server',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text: string) => {
        if (text.startsWith('Error')) {
          return <span style={{ color: 'red' }}>{text}</span>;
        } else if (text === 'Connected') {
          return <span style={{ color: 'green' }}>{text}</span>;
        }
        return text;
      },
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
    },
    {
      title: '节点数',
      dataIndex: 'node_count',
      key: 'node_count',
    },
    {
      title: '默认',
      dataIndex: 'is_default',
      key: 'is_default',
      render: (isDefault: boolean) => isDefault ? <CheckCircleOutlined style={{ color: 'green' }} /> : null,
    },
    {
      title: '操作',
      key: 'action',
      render: (text: string, record: Cluster) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => handleAddOrEditCluster(record)}
          >
            编辑
          </Button>
          {!record.is_default && (
            <>
              <Button
                type="default"
                onClick={() => handleSetDefaultCluster(record.name)}
              >
                设为默认
              </Button>
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDeleteCluster(record.name)}
              >
                删除
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <AppLayout>
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddOrEditCluster()}>
          添加集群
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={clusters}
        rowKey="name"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />

      <Modal
        title={currentCluster ? '编辑集群' : '添加集群'}
        open={modalVisible}
        onOk={handleModalSubmit}
        onCancel={() => setModalVisible(false)}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          name="clusterForm"
        >
          <Form.Item
            name="name"
            label="集群名称"
            rules={[{ required: true, message: '请输入集群名称' }]}
          >
            <Input disabled={!!currentCluster} />
          </Form.Item>
          <Form.Item
            name="server"
            label="服务器地址"
            rules={[{ required: true, message: '请输入服务器地址' }]}
          >
            <Input placeholder="https://kubernetes.example.com" />
          </Form.Item>
          <Form.Item
            name="token"
            label="访问令牌"
            rules={[{ required: !currentCluster, message: '请输入访问令牌' }]}
          >
            <Input.Password placeholder="服务账号令牌" />
          </Form.Item>
          <Form.Item
            name="ca_data"
            label="CA证书数据"
          >
            <Input.TextArea
              placeholder="原始CA证书数据（可选），以-----BEGIN CERTIFICATE-----开头"
              rows={8}
            />
          </Form.Item>
          <Form.Item
            name="is_default"
            label="设为默认集群"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </AppLayout>
  );
};

export default Clusters;
