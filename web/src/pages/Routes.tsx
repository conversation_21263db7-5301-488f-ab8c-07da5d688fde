import React, { useState, useEffect } from 'react';
import { Tabs, Table, Button, Space, Modal, Form, Input, Select, message, Popconfirm, Tag, InputNumber } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { routeApi } from '../services/api';
import { Route, Upstream, UpstreamNode } from '../types';
import AppLayout from '../components/AppLayout';

const { TabPane } = Tabs;
const { Option } = Select;

const Routes: React.FC = () => {
  const [activeTab, setActiveTab] = useState('routes');
  const [routes, setRoutes] = useState<Route[]>([]);
  const [upstreams, setUpstreams] = useState<Upstream[]>([]);
  const [loading, setLoading] = useState(false);
  const [routeModalVisible, setRouteModalVisible] = useState(false);
  const [upstreamModalVisible, setUpstreamModalVisible] = useState(false);
  const [currentRoute, setCurrentRoute] = useState<Route | null>(null);
  const [currentUpstream, setCurrentUpstream] = useState<Upstream | null>(null);
  const [nodes, setNodes] = useState<UpstreamNode[]>([]);
  const [routeForm] = Form.useForm();
  const [upstreamForm] = Form.useForm();
  const [nodeForm] = Form.useForm();

  const fetchRoutes = async () => {
    setLoading(true);
    try {
      const response = await routeApi.getRoutes();
      setRoutes(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch routes:', error);
      message.error('获取路由列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchUpstreams = async () => {
    setLoading(true);
    try {
      const response = await routeApi.getUpstreams();
      setUpstreams(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch upstreams:', error);
      message.error('获取上游服务列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab === 'routes') {
      fetchRoutes();
      fetchUpstreams(); // 需要上游服务列表来填充路由表单
    } else {
      fetchUpstreams();
    }
  }, [activeTab]);

  const handleAddOrEditRoute = (route?: Route) => {
    setCurrentRoute(route || null);
    
    if (route) {
      routeForm.setFieldsValue({
        name: route.name,
        uri: route.uri,
        host: route.host,
        upstream_id: route.upstream_id,
        methods: route.methods,
      });
    } else {
      routeForm.setFieldsValue({
        name: '',
        uri: '',
        host: '',
        upstream_id: undefined,
        methods: ['GET'],
      });
    }
    
    setRouteModalVisible(true);
  };

  const handleAddOrEditUpstream = (upstream?: Upstream) => {
    setCurrentUpstream(upstream || null);
    
    if (upstream) {
      upstreamForm.setFieldsValue({
        name: upstream.name,
        type: upstream.type,
      });
      setNodes(upstream.nodes || []);
    } else {
      upstreamForm.setFieldsValue({
        name: '',
        type: 'roundrobin',
      });
      setNodes([]);
    }
    
    setUpstreamModalVisible(true);
  };

  const handleDeleteRoute = async (id: string) => {
    try {
      await routeApi.deleteRoute(id);
      message.success('路由删除成功');
      fetchRoutes();
    } catch (error) {
      console.error('Failed to delete route:', error);
      message.error('路由删除失败');
    }
  };

  const handleDeleteUpstream = async (id: string) => {
    try {
      await routeApi.deleteUpstream(id);
      message.success('上游服务删除成功');
      fetchUpstreams();
    } catch (error) {
      console.error('Failed to delete upstream:', error);
      message.error('上游服务删除失败');
    }
  };

  const handleRouteSubmit = async () => {
    try {
      const values = await routeForm.validateFields();
      
      if (currentRoute) {
        await routeApi.updateRoute(currentRoute.id, values);
        message.success('路由更新成功');
      } else {
        await routeApi.createRoute(values);
        message.success('路由创建成功');
      }
      
      setRouteModalVisible(false);
      fetchRoutes();
    } catch (error) {
      console.error('Form submission failed:', error);
    }
  };

  const handleUpstreamSubmit = async () => {
    try {
      const values = await upstreamForm.validateFields();
      const upstreamData = {
        ...values,
        nodes,
      };
      
      if (currentUpstream) {
        await routeApi.updateUpstream(currentUpstream.id, upstreamData);
        message.success('上游服务更新成功');
      } else {
        await routeApi.createUpstream(upstreamData);
        message.success('上游服务创建成功');
      }
      
      setUpstreamModalVisible(false);
      fetchUpstreams();
    } catch (error) {
      console.error('Form submission failed:', error);
    }
  };

  const handleAddNode = async () => {
    try {
      const values = await nodeForm.validateFields();
      setNodes([...nodes, values]);
      nodeForm.resetFields();
    } catch (error) {
      console.error('Failed to add node:', error);
    }
  };

  const handleRemoveNode = (index: number) => {
    const newNodes = [...nodes];
    newNodes.splice(index, 1);
    setNodes(newNodes);
  };

  const routeColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'URI',
      dataIndex: 'uri',
      key: 'uri',
    },
    {
      title: '主机',
      dataIndex: 'host',
      key: 'host',
    },
    {
      title: '上游服务',
      dataIndex: 'upstream_id',
      key: 'upstream_id',
      render: (upstreamId: string) => {
        const upstream = upstreams.find(u => u.id === upstreamId);
        return upstream ? upstream.name : upstreamId;
      },
    },
    {
      title: '方法',
      dataIndex: 'methods',
      key: 'methods',
      render: (methods: string[]) => (
        <>
          {methods.map(method => (
            <Tag color="blue" key={method}>
              {method}
            </Tag>
          ))}
        </>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: Route) => (
        <Space size="middle">
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleAddOrEditRoute(record)}
            title="编辑"
          />
          <Popconfirm
            title="确定要删除这个路由吗？"
            onConfirm={() => handleDeleteRoute(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />} title="删除" />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const upstreamColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: '节点数量',
      key: 'nodeCount',
      render: (_: any, record: Upstream) => record.nodes?.length || 0,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: Upstream) => (
        <Space size="middle">
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleAddOrEditUpstream(record)}
            title="编辑"
          />
          <Popconfirm
            title="确定要删除这个上游服务吗？"
            onConfirm={() => handleDeleteUpstream(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />} title="删除" />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const nodeColumns = [
    {
      title: '主机',
      dataIndex: 'host',
      key: 'host',
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
    },
    {
      title: '权重',
      dataIndex: 'weight',
      key: 'weight',
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_: any, _record: any, index: number) => (
        <Button 
          type="text" 
          danger 
          icon={<DeleteOutlined />} 
          onClick={() => handleRemoveNode(index)}
        />
      ),
    },
  ];

  return (
    <AppLayout>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="路由" key="routes">
          <div style={{ marginBottom: 16 }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddOrEditRoute()}>
              新建路由
            </Button>
          </div>

          <Table
            columns={routeColumns}
            dataSource={routes}
            rowKey="id"
            loading={loading && activeTab === 'routes'}
            pagination={{ pageSize: 10 }}
          />
        </TabPane>
        
        <TabPane tab="上游服务" key="upstreams">
          <div style={{ marginBottom: 16 }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddOrEditUpstream()}>
              新建上游服务
            </Button>
          </div>

          <Table
            columns={upstreamColumns}
            dataSource={upstreams}
            rowKey="id"
            loading={loading && activeTab === 'upstreams'}
            pagination={{ pageSize: 10 }}
          />
        </TabPane>
      </Tabs>

      {/* Route Modal */}
      <Modal
        title={currentRoute ? '编辑路由' : '新建路由'}
        open={routeModalVisible}
        onOk={handleRouteSubmit}
        onCancel={() => setRouteModalVisible(false)}
        destroyOnClose
      >
        <Form form={routeForm} layout="vertical">
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="uri"
            label="URI"
            rules={[{ required: true, message: '请输入URI' }]}
          >
            <Input placeholder="/path" />
          </Form.Item>
          <Form.Item
            name="host"
            label="主机"
          >
            <Input placeholder="example.com" />
          </Form.Item>
          <Form.Item
            name="upstream_id"
            label="上游服务"
            rules={[{ required: true, message: '请选择上游服务' }]}
          >
            <Select placeholder="选择上游服务">
              {upstreams.map(upstream => (
                <Option key={upstream.id} value={upstream.id}>{upstream.name}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="methods"
            label="HTTP方法"
            rules={[{ required: true, message: '请选择HTTP方法' }]}
          >
            <Select mode="multiple" placeholder="选择HTTP方法">
              <Option value="GET">GET</Option>
              <Option value="POST">POST</Option>
              <Option value="PUT">PUT</Option>
              <Option value="DELETE">DELETE</Option>
              <Option value="PATCH">PATCH</Option>
              <Option value="HEAD">HEAD</Option>
              <Option value="OPTIONS">OPTIONS</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* Upstream Modal */}
      <Modal
        title={currentUpstream ? '编辑上游服务' : '新建上游服务'}
        open={upstreamModalVisible}
        onOk={handleUpstreamSubmit}
        onCancel={() => setUpstreamModalVisible(false)}
        width={800}
        destroyOnClose
      >
        <Form form={upstreamForm} layout="vertical">
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="type"
            label="负载均衡类型"
            rules={[{ required: true, message: '请选择负载均衡类型' }]}
          >
            <Select>
              <Option value="roundrobin">轮询 (Round Robin)</Option>
              <Option value="chash">一致性哈希 (Consistent Hash)</Option>
              <Option value="ewma">指数加权移动平均 (EWMA)</Option>
            </Select>
          </Form.Item>
        </Form>

        <div style={{ marginTop: 24, marginBottom: 16 }}>
          <h3>节点</h3>
          <Form form={nodeForm} layout="inline" onFinish={handleAddNode}>
            <Form.Item
              name="host"
              rules={[{ required: true, message: '请输入主机' }]}
            >
              <Input placeholder="主机" style={{ width: 150 }} />
            </Form.Item>
            <Form.Item
              name="port"
              rules={[{ required: true, message: '请输入端口' }]}
            >
              <InputNumber placeholder="端口" min={1} max={65535} style={{ width: 100 }} />
            </Form.Item>
            <Form.Item
              name="weight"
              rules={[{ required: true, message: '请输入权重' }]}
              initialValue={1}
            >
              <InputNumber placeholder="权重" min={1} max={100} style={{ width: 100 }} />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                添加节点
              </Button>
            </Form.Item>
          </Form>
        </div>

        <Table
          columns={nodeColumns}
          dataSource={nodes}
          rowKey={(record, index) => `${record.host}-${record.port}-${index}`}
          pagination={false}
        />
      </Modal>
    </AppLayout>
  );
};

export default Routes;
