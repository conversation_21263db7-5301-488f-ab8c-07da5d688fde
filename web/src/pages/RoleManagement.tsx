import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Modal, Form, Input, Select, message, Card, Tabs, Tag, Divider } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, UserOutlined, TeamOutlined, LockOutlined } from '@ant-design/icons';
import { roleApi } from '../services/api';

const { TabPane } = Tabs;
const { Option } = Select;

interface Role {
  id: number;
  name: string;
  description: string;
  is_system: boolean;
  create_time: number;
  update_time: number;
}

interface Permission {
  id: number;
  name: string;
  description: string;
  resource: string;
  action: string;
}

interface UserRole {
  username: string;
  roles: Role[];
}

interface ProjectRole {
  project_id: number;
  project_name: string;
  user_roles: UserRole[];
}

const RoleManagement: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [userRoles, setUserRoles] = useState<UserRole[]>([]);
  const [projectRoles, setProjectRoles] = useState<ProjectRole[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [assignRoleModalVisible, setAssignRoleModalVisible] = useState<boolean>(false);
  const [assignProjectRoleModalVisible, setAssignProjectRoleModalVisible] = useState<boolean>(false);
  const [assignRoleForm] = Form.useForm();
  const [assignProjectRoleForm] = Form.useForm();

  // 获取所有角色
  const fetchRoles = async () => {
    setLoading(true);
    try {
      const response = await roleApi.getRoles();
      if (response.data && response.data.data) {
        setRoles(response.data.data);
      }
    } catch (error: any) {
      console.error('Failed to fetch roles:', error);
      message.error('获取角色列表失败: ' + (error.response?.data?.error || error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 获取角色权限
  const fetchRolePermissions = async (roleName: string) => {
    try {
      const response = await roleApi.getRolePermissions(roleName);
      if (response.data && response.data.data) {
        setPermissions(response.data.data);
      }
    } catch (error: any) {
      console.error('Failed to fetch role permissions:', error);
      message.error('获取角色权限失败: ' + (error.response?.data?.error || error.message || '未知错误'));
    }
  };

  // 初始化
  useEffect(() => {
    fetchRoles();
  }, []);

  // 选择角色时获取权限
  const handleRoleSelect = (role: Role) => {
    setSelectedRole(role);
    fetchRolePermissions(role.name);
  };

  // 分配角色
  const handleAssignRole = () => {
    setAssignRoleModalVisible(true);
    assignRoleForm.resetFields();
  };

  // 分配项目角色
  const handleAssignProjectRole = () => {
    setAssignProjectRoleModalVisible(true);
    assignProjectRoleForm.resetFields();
  };

  // 提交分配角色表单
  const handleAssignRoleSubmit = async () => {
    try {
      const values = await assignRoleForm.validateFields();
      await roleApi.assignRole(values.username, values.role_name);
      message.success('角色分配成功');
      setAssignRoleModalVisible(false);
    } catch (error: any) {
      console.error('Failed to assign role:', error);
      message.error('分配角色失败: ' + (error.response?.data?.error || error.message || '未知错误'));
    }
  };

  // 提交分配项目角色表单
  const handleAssignProjectRoleSubmit = async () => {
    try {
      const values = await assignProjectRoleForm.validateFields();
      await roleApi.assignProjectRole(values.username, values.role_name, values.project_id);
      message.success('项目角色分配成功');
      setAssignProjectRoleModalVisible(false);
    } catch (error: any) {
      console.error('Failed to assign project role:', error);
      message.error('分配项目角色失败: ' + (error.response?.data?.error || error.message || '未知错误'));
    }
  };

  // 角色列表列定义
  const roleColumns = [
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '系统角色',
      dataIndex: 'is_system',
      key: 'is_system',
      render: (isSystem: boolean) => (
        isSystem ? <Tag color="green">是</Tag> : <Tag color="blue">否</Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Role) => (
        <Space size="middle">
          <Button
            type="primary"
            onClick={() => handleRoleSelect(record)}
          >
            查看权限
          </Button>
        </Space>
      ),
    },
  ];

  // 权限列表列定义
  const permissionColumns = [
    {
      title: '权限名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '资源',
      dataIndex: 'resource',
      key: 'resource',
      render: (resource: string) => (
        <Tag color="blue">{resource}</Tag>
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      render: (action: string) => (
        <Tag color="green">{action}</Tag>
      ),
    },
  ];

  return (
    <div className="role-management">
      <Tabs defaultActiveKey="1">
        <TabPane
          tab={
            <span>
              <LockOutlined />
              角色管理
            </span>
          }
          key="1"
        >
          <Card
            title="系统角色"
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAssignRole}
                >
                  分配角色
                </Button>
              </Space>
            }
          >
            <Table
              columns={roleColumns}
              dataSource={roles}
              rowKey="id"
              loading={loading}
            />
          </Card>

          {selectedRole && (
            <Card
              title={`${selectedRole.name} 角色权限`}
              style={{ marginTop: 16 }}
            >
              <Table
                columns={permissionColumns}
                dataSource={permissions}
                rowKey="id"
              />
            </Card>
          )}
        </TabPane>

        <TabPane
          tab={
            <span>
              <TeamOutlined />
              项目角色
            </span>
          }
          key="2"
        >
          <Card
            title="项目角色管理"
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAssignProjectRole}
              >
                分配项目角色
              </Button>
            }
          >
            <p>在这里管理项目特定的角色分配。</p>
            <Divider />
            <p>选择一个项目查看其角色分配情况，或者分配新的项目角色。</p>
          </Card>
        </TabPane>
      </Tabs>

      {/* 分配角色模态框 */}
      <Modal
        title="分配角色"
        open={assignRoleModalVisible}
        onOk={handleAssignRoleSubmit}
        onCancel={() => setAssignRoleModalVisible(false)}
      >
        <Form
          form={assignRoleForm}
          layout="vertical"
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>
          <Form.Item
            name="role_name"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select placeholder="请选择角色">
              {roles.map(role => (
                <Option key={role.id} value={role.name}>{role.name} - {role.description}</Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 分配项目角色模态框 */}
      <Modal
        title="分配项目角色"
        open={assignProjectRoleModalVisible}
        onOk={handleAssignProjectRoleSubmit}
        onCancel={() => setAssignProjectRoleModalVisible(false)}
      >
        <Form
          form={assignProjectRoleForm}
          layout="vertical"
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>
          <Form.Item
            name="project_id"
            label="项目"
            rules={[{ required: true, message: '请选择项目' }]}
          >
            <Select placeholder="请选择项目">
              {/* 这里需要从API获取项目列表 */}
              <Option value={1}>示例项目1</Option>
              <Option value={2}>示例项目2</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="role_name"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select placeholder="请选择角色">
              {roles.map(role => (
                <Option key={role.id} value={role.name}>{role.name} - {role.description}</Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default RoleManagement;
