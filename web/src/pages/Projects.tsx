import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Modal, Form, Input, message, Popconfirm, Tag, Tooltip } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, TagsOutlined } from '@ant-design/icons';
import { projectApi } from '../services/api';
import { Project, ProjectTag } from '../types';
import AppLayout from '../components/AppLayout';
import dayjs from 'dayjs';

const Projects: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [tagModalVisible, setTagModalVisible] = useState(false);
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [projectTags, setProjectTags] = useState<ProjectTag[]>([]);
  const [form] = Form.useForm();
  const [tagForm] = Form.useForm();

  const fetchProjects = async () => {
    setLoading(true);
    try {
      const response = await projectApi.getProjects();
      setProjects(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
      message.error('获取项目列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchProjectTags = async (projectId: number) => {
    try {
      const response = await projectApi.getProjectTags(projectId);
      setProjectTags(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch project tags:', error);
      message.error('获取项目标签失败');
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  const handleAddOrEdit = (project?: Project) => {
    setCurrentProject(project || null);
    form.setFieldsValue(project || {
      project_name: '',
      project_describe: '',
      chart_repo_name: '',
      chart_repo_url: '',
      chart_name: '',
    });
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await projectApi.deleteProject(id);
      message.success('项目删除成功');
      fetchProjects();
    } catch (error) {
      console.error('Failed to delete project:', error);
      message.error('项目删除失败');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (currentProject) {
        await projectApi.updateProject(currentProject.id, values);
        message.success('项目更新成功');
      } else {
        await projectApi.createProject(values);
        message.success('项目创建成功');
      }
      setModalVisible(false);
      fetchProjects();
    } catch (error) {
      console.error('Form submission failed:', error);
    }
  };

  const handleShowTags = (project: Project) => {
    setCurrentProject(project);
    fetchProjectTags(project.id);
    setTagModalVisible(true);
  };

  const handleAddTag = async () => {
    try {
      const values = await tagForm.validateFields();
      if (currentProject) {
        await projectApi.addProjectTag(currentProject.id, values.tag);
        message.success('标签添加成功');
        fetchProjectTags(currentProject.id);
        tagForm.resetFields();
      }
    } catch (error) {
      console.error('Failed to add tag:', error);
      message.error('标签添加失败');
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '项目名称',
      dataIndex: 'project_name',
      key: 'project_name',
    },
    {
      title: '项目描述',
      dataIndex: 'project_describe',
      key: 'project_describe',
      ellipsis: {
        showTitle: false,
      },
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: 'Chart仓库',
      dataIndex: 'chart_repo_name',
      key: 'chart_repo_name',
    },
    {
      title: 'Chart名称',
      dataIndex: 'chart_name',
      key: 'chart_name',
    },
    {
      title: '创建者',
      dataIndex: 'creater',
      key: 'creater',
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: (text: number) => dayjs(text * 1000).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: Project) => (
        <Space size="middle">
          <Button 
            type="text" 
            icon={<TagsOutlined />} 
            onClick={() => handleShowTags(record)}
          />
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleAddOrEdit(record)}
          />
          <Popconfirm
            title="确定要删除这个项目吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <AppLayout>
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddOrEdit()}>
          新建项目
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={projects}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />

      <Modal
        title={currentProject ? '编辑项目' : '新建项目'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="project_name"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="project_describe"
            label="项目描述"
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item
            name="chart_repo_name"
            label="Chart仓库名称"
            rules={[{ required: true, message: '请输入Chart仓库名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="chart_repo_url"
            label="Chart仓库URL"
            rules={[{ required: true, message: '请输入Chart仓库URL' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="chart_name"
            label="Chart名称"
            rules={[{ required: true, message: '请输入Chart名称' }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title={`${currentProject?.project_name || ''} 的标签`}
        open={tagModalVisible}
        onCancel={() => setTagModalVisible(false)}
        footer={null}
      >
        <div style={{ marginBottom: 16 }}>
          <Form form={tagForm} layout="inline" onFinish={handleAddTag}>
            <Form.Item
              name="tag"
              rules={[{ required: true, message: '请输入标签' }]}
              style={{ flex: 1 }}
            >
              <Input placeholder="输入标签" />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                添加标签
              </Button>
            </Form.Item>
          </Form>
        </div>

        <div>
          {projectTags.map(tag => (
            <Tag key={tag.id} color="blue" style={{ margin: '0 8px 8px 0' }}>
              {tag.tag}
            </Tag>
          ))}
          {projectTags.length === 0 && <p>暂无标签</p>}
        </div>
      </Modal>
    </AppLayout>
  );
};

export default Projects;
