import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message } from 'antd';
import { MailOutlined, UserOutlined, LockOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import AppLayout from '../components/AppLayout';

const { Title } = Typography;

const Profile: React.FC = () => {
  const { user, updateProfile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const handleUpdate = async (values: any) => {
    // 如果密码为空，则不更新密码
    if (!values.password) {
      delete values.password;
    }
    
    setLoading(true);
    try {
      await updateProfile(values);
      message.success('个人信息更新成功');
    } catch (error: any) {
      message.error(error.response?.data?.error || '更新失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AppLayout>
      <Card style={{ maxWidth: 600, margin: '0 auto' }}>
        <Title level={2} style={{ marginBottom: 24 }}>个人信息</Title>
        
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            username: user?.username,
            email: user?.email,
            fullName: user?.fullName || '',
          }}
          onFinish={handleUpdate}
        >
          <Form.Item
            name="username"
            label="用户名"
          >
            <Input prefix={<UserOutlined />} disabled />
          </Form.Item>

          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input prefix={<MailOutlined />} />
          </Form.Item>

          <Form.Item
            name="fullName"
            label="姓名"
          >
            <Input prefix={<UserOutlined />} />
          </Form.Item>

          <Form.Item
            name="password"
            label="新密码"
            rules={[
              { min: 6, message: '密码长度至少为6个字符' }
            ]}
            extra="如不修改密码，请留空"
          >
            <Input.Password prefix={<LockOutlined />} />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              更新信息
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </AppLayout>
  );
};

export default Profile;
