import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Modal, Form, Input, Select, message, Popconfirm, Tabs, Tag, Tooltip } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, CloudUploadOutlined, HistoryOutlined, CodeOutlined } from '@ant-design/icons';
import { releaseApi, projectApi } from '../services/api';
import { Release, Project, ReleaseHistory } from '../types';
import AppLayout from '../components/AppLayout';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

const Releases: React.FC = () => {
  const [releases, setReleases] = useState<Release[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [deployModalVisible, setDeployModalVisible] = useState(false);
  const [historyModalVisible, setHistoryModalVisible] = useState(false);
  const [valuesModalVisible, setValuesModalVisible] = useState(false);
  const [currentRelease, setCurrentRelease] = useState<Release | null>(null);
  const [releaseHistories, setReleaseHistories] = useState<ReleaseHistory[]>([]);
  const [releaseValues, setReleaseValues] = useState<string>('');
  const [form] = Form.useForm();
  const [deployForm] = Form.useForm();

  const fetchReleases = async () => {
    setLoading(true);
    try {
      const response = await releaseApi.getReleases();
      setReleases(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch releases:', error);
      message.error('获取发布列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchProjects = async () => {
    try {
      const response = await projectApi.getProjects();
      setProjects(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
      message.error('获取项目列表失败');
    }
  };

  const fetchReleaseHistory = async (releaseId: number) => {
    try {
      const response = await releaseApi.getReleaseHistory(releaseId);
      setReleaseHistories(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch release history:', error);
      message.error('获取发布历史失败');
    }
  };

  useEffect(() => {
    fetchReleases();
    fetchProjects();
  }, []);

  const handleAddOrEdit = (release?: Release) => {
    setCurrentRelease(release || null);
    form.setFieldsValue(release || {
      project_id: undefined,
      release_name: '',
      cluster_name: '',
      cluster_namespace: 'default',
      chart_version: '',
      chart_values: '',
      release_env: 1,
    });
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await releaseApi.deleteRelease(id);
      message.success('发布删除成功');
      fetchReleases();
    } catch (error) {
      console.error('Failed to delete release:', error);
      message.error('发布删除失败');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (currentRelease) {
        await releaseApi.updateRelease(currentRelease.id, values);
        message.success('发布更新成功');
      } else {
        await releaseApi.createRelease(values);
        message.success('发布创建成功');
      }
      setModalVisible(false);
      fetchReleases();
    } catch (error) {
      console.error('Form submission failed:', error);
    }
  };

  const handleDeploy = (release: Release) => {
    setCurrentRelease(release);
    deployForm.resetFields();
    setDeployModalVisible(true);
  };

  const handleDeploySubmit = async () => {
    try {
      const values = await deployForm.validateFields();
      if (currentRelease) {
        await releaseApi.deployRelease(currentRelease.id, values);
        message.success('部署成功');
        setDeployModalVisible(false);
        fetchReleases();
      }
    } catch (error) {
      console.error('Deploy failed:', error);
      message.error('部署失败');
    }
  };

  const handleShowHistory = (release: Release) => {
    setCurrentRelease(release);
    fetchReleaseHistory(release.id);
    setHistoryModalVisible(true);
  };

  const handleShowValues = (release: Release) => {
    setCurrentRelease(release);
    setReleaseValues(release.chart_values);
    setValuesModalVisible(true);
  };

  const getProjectName = (projectId: number) => {
    const project = projects.find(p => p.id === projectId);
    return project ? project.project_name : '未知项目';
  };

  const getReleaseEnvLabel = (env: number) => {
    switch (env) {
      case 1:
        return <Tag color="blue">开发环境</Tag>;
      case 2:
        return <Tag color="orange">测试环境</Tag>;
      case 3:
        return <Tag color="green">生产环境</Tag>;
      default:
        return <Tag>未知环境</Tag>;
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '项目',
      dataIndex: 'project_id',
      key: 'project_id',
      render: (projectId: number) => getProjectName(projectId),
    },
    {
      title: '发布名称',
      dataIndex: 'release_name',
      key: 'release_name',
    },
    {
      title: '集群',
      dataIndex: 'cluster_name',
      key: 'cluster_name',
    },
    {
      title: '命名空间',
      dataIndex: 'cluster_namespace',
      key: 'cluster_namespace',
    },
    {
      title: 'Chart版本',
      dataIndex: 'chart_version',
      key: 'chart_version',
    },
    {
      title: '环境',
      dataIndex: 'release_env',
      key: 'release_env',
      render: (env: number) => getReleaseEnvLabel(env),
    },
    {
      title: '创建者',
      dataIndex: 'creater',
      key: 'creater',
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: (text: number) => dayjs(text * 1000).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: Release) => (
        <Space size="middle">
          <Button 
            type="text" 
            icon={<CloudUploadOutlined />} 
            onClick={() => handleDeploy(record)}
            title="部署"
          />
          <Button 
            type="text" 
            icon={<HistoryOutlined />} 
            onClick={() => handleShowHistory(record)}
            title="历史"
          />
          <Button 
            type="text" 
            icon={<CodeOutlined />} 
            onClick={() => handleShowValues(record)}
            title="配置"
          />
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleAddOrEdit(record)}
            title="编辑"
          />
          <Popconfirm
            title="确定要删除这个发布吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />} title="删除" />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const historyColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '操作类型',
      dataIndex: 'h_type',
      key: 'h_type',
    },
    {
      title: '旧版本',
      dataIndex: 'prev_chart_version',
      key: 'prev_chart_version',
    },
    {
      title: '新版本',
      dataIndex: 'chart_version',
      key: 'chart_version',
    },
    {
      title: '旧镜像标签',
      dataIndex: 'prev_image_tag',
      key: 'prev_image_tag',
    },
    {
      title: '新镜像标签',
      dataIndex: 'image_tag',
      key: 'image_tag',
    },
    {
      title: '操作者',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '变更日志',
      dataIndex: 'change_log',
      key: 'change_log',
      ellipsis: {
        showTitle: false,
      },
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '操作时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: (text: number) => dayjs(text * 1000).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  return (
    <AppLayout>
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddOrEdit()}>
          新建发布
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={releases}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />

      <Modal
        title={currentRelease ? '编辑发布' : '新建发布'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={800}
        destroyOnClose
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="project_id"
            label="项目"
            rules={[{ required: true, message: '请选择项目' }]}
          >
            <Select placeholder="选择项目">
              {projects.map(project => (
                <Option key={project.id} value={project.id}>{project.project_name}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="release_name"
            label="发布名称"
            rules={[{ required: true, message: '请输入发布名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="cluster_name"
            label="集群名称"
            rules={[{ required: true, message: '请输入集群名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="cluster_namespace"
            label="命名空间"
            rules={[{ required: true, message: '请输入命名空间' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="chart_version"
            label="Chart版本"
            rules={[{ required: true, message: '请输入Chart版本' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="release_env"
            label="环境"
            rules={[{ required: true, message: '请选择环境' }]}
          >
            <Select placeholder="选择环境">
              <Option value={1}>开发环境</Option>
              <Option value={2}>测试环境</Option>
              <Option value={3}>生产环境</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="chart_values"
            label="Chart配置值"
          >
            <TextArea rows={10} />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="部署发布"
        open={deployModalVisible}
        onOk={handleDeploySubmit}
        onCancel={() => setDeployModalVisible(false)}
        destroyOnClose
      >
        <Form form={deployForm} layout="vertical">
          <Form.Item
            name="change_log"
            label="变更日志"
            rules={[{ required: true, message: '请输入变更日志' }]}
          >
            <TextArea rows={4} placeholder="请描述此次部署的变更内容" />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title={`${currentRelease?.release_name || ''} 的发布历史`}
        open={historyModalVisible}
        onCancel={() => setHistoryModalVisible(false)}
        width={1000}
        footer={null}
      >
        <Table
          columns={historyColumns}
          dataSource={releaseHistories}
          rowKey="id"
          pagination={{ pageSize: 5 }}
        />
      </Modal>

      <Modal
        title={`${currentRelease?.release_name || ''} 的配置值`}
        open={valuesModalVisible}
        onCancel={() => setValuesModalVisible(false)}
        width={800}
        footer={null}
      >
        <pre style={{ 
          backgroundColor: '#f5f5f5', 
          padding: 16, 
          borderRadius: 4,
          maxHeight: '60vh',
          overflow: 'auto'
        }}>
          {releaseValues}
        </pre>
      </Modal>
    </AppLayout>
  );
};

export default Releases;
