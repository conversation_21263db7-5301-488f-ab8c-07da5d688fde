import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Modal, Form, Input, DatePicker, Select, message, Popconfirm, Tabs, Tag, Tooltip } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined, ScheduleOutlined, UnorderedListOutlined } from '@ant-design/icons';
import { planApi, projectApi, releaseApi } from '../services/api';
import { ReleasePlan, PlanItem, PlanItemRelease, Project, Release } from '../types';
import AppLayout from '../components/AppLayout';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

const Plans: React.FC = () => {
  const [plans, setPlans] = useState<ReleasePlan[]>([]);
  const [planItems, setPlanItems] = useState<PlanItem[]>([]);
  const [planItemReleases, setPlanItemReleases] = useState<PlanItemRelease[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [releases, setReleases] = useState<Release[]>([]);
  const [loading, setLoading] = useState(false);
  const [planModalVisible, setPlanModalVisible] = useState(false);
  const [itemModalVisible, setItemModalVisible] = useState(false);
  const [releaseModalVisible, setReleaseModalVisible] = useState(false);
  const [currentPlan, setCurrentPlan] = useState<ReleasePlan | null>(null);
  const [currentItem, setCurrentItem] = useState<PlanItem | null>(null);
  const [currentItemId, setCurrentItemId] = useState<number | null>(null);
  const [planForm] = Form.useForm();
  const [itemForm] = Form.useForm();
  const [releaseForm] = Form.useForm();

  const fetchPlans = async () => {
    setLoading(true);
    try {
      const response = await planApi.getPlans();
      setPlans(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch plans:', error);
      message.error('获取计划列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchProjects = async () => {
    try {
      const response = await projectApi.getProjects();
      setProjects(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
      message.error('获取项目列表失败');
    }
  };

  const fetchReleases = async (projectId?: number) => {
    try {
      const response = await releaseApi.getReleases(projectId);
      setReleases(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch releases:', error);
      message.error('获取发布列表失败');
    }
  };

  const fetchPlanItems = async (planId: number) => {
    try {
      const response = await planApi.getPlanItems(planId);
      setPlanItems(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch plan items:', error);
      message.error('获取计划项列表失败');
    }
  };

  const fetchPlanItemReleases = async (itemId: number) => {
    try {
      const response = await planApi.getPlanItemReleases(itemId);
      setPlanItemReleases(response.data.data || []);
    } catch (error) {
      console.error('Failed to fetch plan item releases:', error);
      message.error('获取计划项发布列表失败');
    }
  };

  useEffect(() => {
    fetchPlans();
    fetchProjects();
  }, []);

  const handleAddOrEditPlan = (plan?: ReleasePlan) => {
    setCurrentPlan(plan || null);
    
    if (plan) {
      planForm.setFieldsValue({
        plan_name: plan.plan_name,
        plan_status: plan.plan_status,
        plan_start_time: plan.plan_start_time ? dayjs(plan.plan_start_time * 1000) : undefined,
        plan_end_time: plan.plan_end_time ? dayjs(plan.plan_end_time * 1000) : undefined,
      });
    } else {
      planForm.setFieldsValue({
        plan_name: '',
        plan_status: 'draft',
        plan_start_time: undefined,
        plan_end_time: undefined,
      });
    }
    
    setPlanModalVisible(true);
  };

  const handleAddOrEditItem = (planId: number, item?: PlanItem) => {
    setCurrentPlan({ id: planId } as ReleasePlan);
    setCurrentItem(item || null);
    
    if (item) {
      itemForm.setFieldsValue({
        project_id: item.project_id,
        item_type: item.item_type,
        item_seq: item.item_seq,
        item_status: item.item_status,
        issue_no: item.issue_no,
        tester: item.tester,
        pm_name: item.pm_name,
        byroad_change: item.byroad_change,
        meman_change: item.meman_change,
        db_change: item.db_change,
        dove_change: item.dove_change,
        other_change: item.other_change,
        comment: item.comment,
      });
    } else {
      itemForm.setFieldsValue({
        project_id: undefined,
        item_type: 'deploy',
        item_seq: 1,
        item_status: 'pending',
        issue_no: '',
        tester: '',
        pm_name: '',
        byroad_change: 'no',
        meman_change: 'no',
        db_change: 'no',
        dove_change: 'no',
        other_change: 'no',
        comment: '',
      });
    }
    
    setItemModalVisible(true);
  };

  const handleAddRelease = (itemId: number) => {
    setCurrentItemId(itemId);
    releaseForm.resetFields();
    setReleaseModalVisible(true);
  };

  const handleDeletePlan = async (id: number) => {
    try {
      await planApi.deletePlan(id);
      message.success('计划删除成功');
      fetchPlans();
    } catch (error) {
      console.error('Failed to delete plan:', error);
      message.error('计划删除失败');
    }
  };

  const handleDeleteItem = async (planId: number, itemId: number) => {
    try {
      await planApi.deletePlanItem(planId, itemId);
      message.success('计划项删除成功');
      fetchPlanItems(planId);
    } catch (error) {
      console.error('Failed to delete plan item:', error);
      message.error('计划项删除失败');
    }
  };

  const handlePlanSubmit = async () => {
    try {
      const values = await planForm.validateFields();
      
      // 转换日期为时间戳
      if (values.plan_start_time) {
        values.plan_start_time = Math.floor(values.plan_start_time.valueOf() / 1000);
      }
      if (values.plan_end_time) {
        values.plan_end_time = Math.floor(values.plan_end_time.valueOf() / 1000);
      }
      
      if (currentPlan?.id) {
        await planApi.updatePlan(currentPlan.id, values);
        message.success('计划更新成功');
      } else {
        await planApi.createPlan(values);
        message.success('计划创建成功');
      }
      
      setPlanModalVisible(false);
      fetchPlans();
    } catch (error) {
      console.error('Form submission failed:', error);
    }
  };

  const handleItemSubmit = async () => {
    try {
      const values = await itemForm.validateFields();
      
      if (currentPlan?.id) {
        if (currentItem?.id) {
          await planApi.updatePlanItem(currentPlan.id, currentItem.id, values);
          message.success('计划项更新成功');
        } else {
          await planApi.createPlanItem(currentPlan.id, values);
          message.success('计划项创建成功');
        }
        
        setItemModalVisible(false);
        fetchPlanItems(currentPlan.id);
      }
    } catch (error) {
      console.error('Form submission failed:', error);
    }
  };

  const handleReleaseSubmit = async () => {
    try {
      const values = await releaseForm.validateFields();
      
      if (currentItemId) {
        await planApi.createPlanItemRelease(currentItemId, values);
        message.success('发布项添加成功');
        setReleaseModalVisible(false);
        fetchPlanItemReleases(currentItemId);
      }
    } catch (error) {
      console.error('Form submission failed:', error);
    }
  };

  const handleViewItems = (plan: ReleasePlan) => {
    setCurrentPlan(plan);
    fetchPlanItems(plan.id);
  };

  const handleViewReleases = (itemId: number) => {
    setCurrentItemId(itemId);
    fetchPlanItemReleases(itemId);
  };

  const handleProjectChange = (projectId: number) => {
    fetchReleases(projectId);
  };

  const getPlanStatusTag = (status: string) => {
    switch (status) {
      case 'draft':
        return <Tag color="default">草稿</Tag>;
      case 'pending':
        return <Tag color="blue">待执行</Tag>;
      case 'executing':
        return <Tag color="orange">执行中</Tag>;
      case 'completed':
        return <Tag color="green">已完成</Tag>;
      case 'failed':
        return <Tag color="red">失败</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const getItemStatusTag = (status: string) => {
    switch (status) {
      case 'pending':
        return <Tag color="blue">待执行</Tag>;
      case 'executing':
        return <Tag color="orange">执行中</Tag>;
      case 'completed':
        return <Tag color="green">已完成</Tag>;
      case 'failed':
        return <Tag color="red">失败</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const getReleaseStatusTag = (status: string) => {
    switch (status) {
      case 'pending':
        return <Tag color="blue">待发布</Tag>;
      case 'executing':
        return <Tag color="orange">发布中</Tag>;
      case 'completed':
        return <Tag color="green">已发布</Tag>;
      case 'failed':
        return <Tag color="red">失败</Tag>;
      case 'rollback':
        return <Tag color="purple">已回滚</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const getProjectName = (projectId: number) => {
    const project = projects.find(p => p.id === projectId);
    return project ? project.project_name : '未知项目';
  };

  const getReleaseName = (releaseId: number) => {
    const release = releases.find(r => r.id === releaseId);
    return release ? release.release_name : '未知发布';
  };

  const planColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '计划名称',
      dataIndex: 'plan_name',
      key: 'plan_name',
    },
    {
      title: '状态',
      dataIndex: 'plan_status',
      key: 'plan_status',
      render: (status: string) => getPlanStatusTag(status),
    },
    {
      title: '开始时间',
      dataIndex: 'plan_start_time',
      key: 'plan_start_time',
      render: (time: number) => time ? dayjs(time * 1000).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '结束时间',
      dataIndex: 'plan_end_time',
      key: 'plan_end_time',
      render: (time: number) => time ? dayjs(time * 1000).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '创建者',
      dataIndex: 'creater',
      key: 'creater',
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: (time: number) => dayjs(time * 1000).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      render: (_: any, record: ReleasePlan) => (
        <Space size="middle">
          <Button 
            type="text" 
            icon={<UnorderedListOutlined />} 
            onClick={() => handleViewItems(record)}
            title="查看项目"
          />
          <Button 
            type="text" 
            icon={<PlayCircleOutlined />} 
            title="执行计划"
          />
          <Button 
            type="text" 
            icon={<ScheduleOutlined />} 
            title="调度计划"
          />
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleAddOrEditPlan(record)}
            title="编辑"
          />
          <Popconfirm
            title="确定要删除这个计划吗？"
            onConfirm={() => handleDeletePlan(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />} title="删除" />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const itemColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '项目',
      dataIndex: 'project_id',
      key: 'project_id',
      render: (projectId: number) => getProjectName(projectId),
    },
    {
      title: '类型',
      dataIndex: 'item_type',
      key: 'item_type',
    },
    {
      title: '序号',
      dataIndex: 'item_seq',
      key: 'item_seq',
    },
    {
      title: '状态',
      dataIndex: 'item_status',
      key: 'item_status',
      render: (status: string) => getItemStatusTag(status),
    },
    {
      title: '问题编号',
      dataIndex: 'issue_no',
      key: 'issue_no',
    },
    {
      title: '测试人员',
      dataIndex: 'tester',
      key: 'tester',
    },
    {
      title: '产品经理',
      dataIndex: 'pm_name',
      key: 'pm_name',
    },
    {
      title: '备注',
      dataIndex: 'comment',
      key: 'comment',
      ellipsis: {
        showTitle: false,
      },
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: PlanItem) => (
        <Space size="middle">
          <Button 
            type="text" 
            icon={<UnorderedListOutlined />} 
            onClick={() => handleViewReleases(record.id)}
            title="查看发布"
          />
          <Button 
            type="text" 
            icon={<PlusOutlined />} 
            onClick={() => handleAddRelease(record.id)}
            title="添加发布"
          />
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleAddOrEditItem(currentPlan!.id, record)}
            title="编辑"
          />
          <Popconfirm
            title="确定要删除这个计划项吗？"
            onConfirm={() => handleDeleteItem(currentPlan!.id, record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />} title="删除" />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const releaseColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '发布',
      dataIndex: 'release_id',
      key: 'release_id',
      render: (releaseId: number) => getReleaseName(releaseId),
    },
    {
      title: '目标标签',
      dataIndex: 'target_tag',
      key: 'target_tag',
    },
    {
      title: '回滚标签',
      dataIndex: 'rollback_tag',
      key: 'rollback_tag',
    },
    {
      title: '状态',
      dataIndex: 'release_status',
      key: 'release_status',
      render: (status: string) => getReleaseStatusTag(status),
    },
    {
      title: '错误信息',
      dataIndex: 'err_msg',
      key: 'err_msg',
      ellipsis: {
        showTitle: false,
      },
      render: (text: string) => text ? (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ) : '-',
    },
    {
      title: '变更日志',
      dataIndex: 'change_log',
      key: 'change_log',
      ellipsis: {
        showTitle: false,
      },
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: (time: number) => dayjs(time * 1000).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  return (
    <AppLayout>
      <Tabs defaultActiveKey="plans">
        <TabPane tab="发布计划" key="plans">
          <div style={{ marginBottom: 16 }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAddOrEditPlan()}>
              新建计划
            </Button>
          </div>

          <Table
            columns={planColumns}
            dataSource={plans}
            rowKey="id"
            loading={loading}
            pagination={{ pageSize: 10 }}
          />
        </TabPane>
        
        {currentPlan && (
          <TabPane tab={`${currentPlan.plan_name || ''} 的计划项`} key="items">
            <div style={{ marginBottom: 16 }}>
              <Button 
                type="primary" 
                icon={<PlusOutlined />} 
                onClick={() => handleAddOrEditItem(currentPlan.id)}
              >
                添加计划项
              </Button>
            </div>

            <Table
              columns={itemColumns}
              dataSource={planItems}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
        )}
        
        {currentItemId && (
          <TabPane tab="发布项" key="releases">
            <Table
              columns={releaseColumns}
              dataSource={planItemReleases}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
        )}
      </Tabs>

      {/* Plan Modal */}
      <Modal
        title={currentPlan ? '编辑计划' : '新建计划'}
        open={planModalVisible}
        onOk={handlePlanSubmit}
        onCancel={() => setPlanModalVisible(false)}
        destroyOnClose
      >
        <Form form={planForm} layout="vertical">
          <Form.Item
            name="plan_name"
            label="计划名称"
            rules={[{ required: true, message: '请输入计划名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="plan_status"
            label="计划状态"
            rules={[{ required: true, message: '请选择计划状态' }]}
          >
            <Select>
              <Option value="draft">草稿</Option>
              <Option value="pending">待执行</Option>
              <Option value="executing">执行中</Option>
              <Option value="completed">已完成</Option>
              <Option value="failed">失败</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="plan_start_time"
            label="计划开始时间"
          >
            <DatePicker showTime style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="plan_end_time"
            label="计划结束时间"
          >
            <DatePicker showTime style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>

      {/* Item Modal */}
      <Modal
        title={currentItem ? '编辑计划项' : '新建计划项'}
        open={itemModalVisible}
        onOk={handleItemSubmit}
        onCancel={() => setItemModalVisible(false)}
        width={800}
        destroyOnClose
      >
        <Form form={itemForm} layout="vertical">
          <Form.Item
            name="project_id"
            label="项目"
            rules={[{ required: true, message: '请选择项目' }]}
          >
            <Select placeholder="选择项目" onChange={handleProjectChange}>
              {projects.map(project => (
                <Option key={project.id} value={project.id}>{project.project_name}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="item_type"
            label="类型"
            rules={[{ required: true, message: '请选择类型' }]}
          >
            <Select>
              <Option value="deploy">部署</Option>
              <Option value="rollback">回滚</Option>
              <Option value="config">配置变更</Option>
              <Option value="other">其他</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="item_seq"
            label="序号"
            rules={[{ required: true, message: '请输入序号' }]}
          >
            <Input type="number" />
          </Form.Item>
          <Form.Item
            name="item_status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select>
              <Option value="pending">待执行</Option>
              <Option value="executing">执行中</Option>
              <Option value="completed">已完成</Option>
              <Option value="failed">失败</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="issue_no"
            label="问题编号"
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="tester"
            label="测试人员"
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="pm_name"
            label="产品经理"
          >
            <Input />
          </Form.Item>
          
          <Form.Item label="变更类型">
            <Space>
              <Form.Item name="byroad_change" noStyle>
                <Select style={{ width: 150 }}>
                  <Option value="yes">旁路变更</Option>
                  <Option value="no">无旁路变更</Option>
                </Select>
              </Form.Item>
              <Form.Item name="meman_change" noStyle>
                <Select style={{ width: 150 }}>
                  <Option value="yes">内存变更</Option>
                  <Option value="no">无内存变更</Option>
                </Select>
              </Form.Item>
              <Form.Item name="db_change" noStyle>
                <Select style={{ width: 150 }}>
                  <Option value="yes">数据库变更</Option>
                  <Option value="no">无数据库变更</Option>
                </Select>
              </Form.Item>
            </Space>
          </Form.Item>
          
          <Form.Item label="其他变更">
            <Space>
              <Form.Item name="dove_change" noStyle>
                <Select style={{ width: 150 }}>
                  <Option value="yes">Dove变更</Option>
                  <Option value="no">无Dove变更</Option>
                </Select>
              </Form.Item>
              <Form.Item name="other_change" noStyle>
                <Select style={{ width: 150 }}>
                  <Option value="yes">其他变更</Option>
                  <Option value="no">无其他变更</Option>
                </Select>
              </Form.Item>
            </Space>
          </Form.Item>
          
          <Form.Item
            name="comment"
            label="备注"
          >
            <TextArea rows={4} />
          </Form.Item>
        </Form>
      </Modal>

      {/* Release Modal */}
      <Modal
        title="添加发布项"
        open={releaseModalVisible}
        onOk={handleReleaseSubmit}
        onCancel={() => setReleaseModalVisible(false)}
        destroyOnClose
      >
        <Form form={releaseForm} layout="vertical">
          <Form.Item
            name="release_id"
            label="发布"
            rules={[{ required: true, message: '请选择发布' }]}
          >
            <Select placeholder="选择发布">
              {releases.map(release => (
                <Option key={release.id} value={release.id}>{release.release_name}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="target_tag"
            label="目标标签"
            rules={[{ required: true, message: '请输入目标标签' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="rollback_tag"
            label="回滚标签"
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="release_status"
            label="状态"
            initialValue="pending"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select>
              <Option value="pending">待发布</Option>
              <Option value="executing">发布中</Option>
              <Option value="completed">已发布</Option>
              <Option value="failed">失败</Option>
              <Option value="rollback">已回滚</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="change_log"
            label="变更日志"
            rules={[{ required: true, message: '请输入变更日志' }]}
          >
            <TextArea rows={4} />
          </Form.Item>
        </Form>
      </Modal>
    </AppLayout>
  );
};

export default Plans;
