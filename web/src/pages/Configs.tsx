import React, { useState, useEffect } from 'react';
import { Tabs, Table, Button, Space, Modal, Form, Input, Select, message, Popconfirm, Card, Typography, Tooltip } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, SaveOutlined } from '@ant-design/icons';
import { configApi, clusterApi } from '../services/api';
import { Secret, ConfigMap, Cluster } from '../types';
import AppLayout from '../components/AppLayout';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;

// Base64解码函数
const decodeBase64 = (str: string): string => {
  try {
    return atob(str);
  } catch (e) {
    console.error('Failed to decode base64 string:', e);
    return str;
  }
};

// Base64编码函数
const encodeBase64 = (str: string): string => {
  try {
    return btoa(str);
  } catch (e) {
    console.error('Failed to encode string to base64:', e);
    return str;
  }
};

const Configs: React.FC = () => {
  const [activeTab, setActiveTab] = useState('secrets');
  const [secrets, setSecrets] = useState<Secret[]>([]);
  const [configMaps, setConfigMaps] = useState<ConfigMap[]>([]);
  const [clusters, setClusters] = useState<Cluster[]>([]);
  const [namespaces, setNamespaces] = useState<string[]>(['default']);
  const [selectedCluster, setSelectedCluster] = useState<string>('');
  const [selectedNamespace, setSelectedNamespace] = useState<string>('default');
  const [loading, setLoading] = useState(false);
  const [secretModalVisible, setSecretModalVisible] = useState(false);
  const [configMapModalVisible, setConfigMapModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentSecret, setCurrentSecret] = useState<Secret | null>(null);
  const [currentConfigMap, setCurrentConfigMap] = useState<ConfigMap | null>(null);
  const [currentData, setCurrentData] = useState<Record<string, string>>({});
  const [editingKey, setEditingKey] = useState<string>('');
  const [editingValue, setEditingValue] = useState<string>('');
  const [isSecret, setIsSecret] = useState<boolean>(false);
  const [secretForm] = Form.useForm();
  const [configMapForm] = Form.useForm();
  const [dataForm] = Form.useForm();

  const fetchSecrets = async () => {
    if (!selectedCluster || !selectedNamespace) return;

    setLoading(true);
    try {
      const response = await configApi.getSecrets(selectedNamespace, selectedCluster);
      setSecrets(response.data.data || []);
    } catch (error: any) {
      console.error('Failed to fetch secrets:', error);
      message.error('获取Secret列表失败: ' + (error.response?.data?.error || error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  const fetchConfigMaps = async () => {
    if (!selectedCluster || !selectedNamespace) return;

    setLoading(true);
    try {
      const response = await configApi.getConfigMaps(selectedNamespace, selectedCluster);
      setConfigMaps(response.data.data || []);
    } catch (error: any) {
      console.error('Failed to fetch configmaps:', error);
      message.error('获取ConfigMap列表失败: ' + (error.response?.data?.error || error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  const fetchClusters = async () => {
    try {
      const response = await clusterApi.getClusters();
      const clusterList = response.data.data || [];
      setClusters(clusterList);

      // 设置默认集群
      const defaultCluster = clusterList.find(c => c.is_default);
      if (defaultCluster) {
        setSelectedCluster(defaultCluster.name);
      } else if (clusterList.length > 0) {
        setSelectedCluster(clusterList[0].name);
      }
    } catch (error: any) {
      console.error('Failed to fetch clusters:', error);
      message.error('获取集群列表失败: ' + (error.response?.data?.error || error.message || '未知错误'));
    }
  };

  // 当集群或命名空间变化时重新获取数据
  useEffect(() => {
    if (activeTab === 'secrets') {
      fetchSecrets();
    } else {
      fetchConfigMaps();
    }
  }, [activeTab, selectedCluster, selectedNamespace]);

  // 初始化时获取集群列表
  useEffect(() => {
    fetchClusters();
  }, []);

  // 当集群变化时获取命名空间列表
  useEffect(() => {
    if (selectedCluster) {
      fetchNamespaces(selectedCluster);
    }
  }, [selectedCluster]);

  const handleAddOrEditSecret = (secret?: Secret) => {
    setCurrentSecret(secret || null);
    setIsSecret(true);

    if (secret) {
      secretForm.setFieldsValue({
        name: secret.name,
        namespace: secret.namespace,
        cluster: selectedCluster,
      });

      // 解码Secret数据
      const decodedData: Record<string, string> = {};
      Object.entries(secret.data || {}).forEach(([key, value]) => {
        decodedData[key] = decodeBase64(value);
      });

      setCurrentData(decodedData);
    } else {
      secretForm.setFieldsValue({
        name: '',
        namespace: selectedNamespace,
        cluster: selectedCluster,
      });
      setCurrentData({});
    }

    setSecretModalVisible(true);
  };

  const handleAddOrEditConfigMap = (configMap?: ConfigMap) => {
    setCurrentConfigMap(configMap || null);
    setIsSecret(false);

    if (configMap) {
      configMapForm.setFieldsValue({
        name: configMap.name,
        namespace: configMap.namespace,
        cluster: selectedCluster,
      });
      setCurrentData(configMap.data || {});
    } else {
      configMapForm.setFieldsValue({
        name: '',
        namespace: selectedNamespace,
        cluster: selectedCluster,
      });
      setCurrentData({});
    }

    setConfigMapModalVisible(true);
  };

  const handleDeleteSecret = async (name: string, namespace: string) => {
    try {
      await configApi.deleteSecret(name, namespace, selectedCluster);
      message.success('Secret删除成功');
      fetchSecrets();
    } catch (error: any) {
      console.error('Failed to delete secret:', error);
      message.error('Secret删除失败: ' + (error.response?.data?.error || error.message || '未知错误'));
    }
  };

  const handleDeleteConfigMap = async (name: string, namespace: string) => {
    try {
      await configApi.deleteConfigMap(name, namespace, selectedCluster);
      message.success('ConfigMap删除成功');
      fetchConfigMaps();
    } catch (error: any) {
      console.error('Failed to delete configmap:', error);
      message.error('ConfigMap删除失败: ' + (error.response?.data?.error || error.message || '未知错误'));
    }
  };

  const handleSecretSubmit = async () => {
    try {
      const values = await secretForm.validateFields();

      // 确保至少有一个数据项
      if (Object.keys(currentData).length === 0) {
        message.error('请至少添加一个数据项');
        return;
      }

      // 编码Secret数据
      const encodedData: Record<string, string> = {};
      Object.entries(currentData).forEach(([key, value]) => {
        encodedData[key] = encodeBase64(value);
      });

      const secretData = {
        name: values.name,
        namespace: values.namespace || selectedNamespace,
        data: encodedData,
        labels: { app: values.name } // 添加默认标签
      };

      if (currentSecret) {
        await configApi.updateSecret(values.name, values.namespace || selectedNamespace, secretData, selectedCluster);
        message.success('Secret更新成功');
      } else {
        await configApi.createSecret(secretData, selectedCluster);
        message.success('Secret创建成功');
      }

      setSecretModalVisible(false);
      fetchSecrets();
    } catch (error: any) {
      console.error('Form submission failed:', error);
      message.error('提交失败: ' + (error.response?.data?.error || error.message || '未知错误'));
    }
  };

  const handleConfigMapSubmit = async () => {
    try {
      const values = await configMapForm.validateFields();

      // 确保至少有一个数据项
      if (Object.keys(currentData).length === 0) {
        message.error('请至少添加一个数据项');
        return;
      }

      const configMapData = {
        name: values.name,
        namespace: values.namespace || selectedNamespace,
        data: currentData,
        labels: { app: values.name } // 添加默认标签
      };

      if (currentConfigMap) {
        await configApi.updateConfigMap(values.name, values.namespace || selectedNamespace, configMapData, selectedCluster);
        message.success('ConfigMap更新成功');
      } else {
        await configApi.createConfigMap(configMapData, selectedCluster);
        message.success('ConfigMap创建成功');
      }

      setConfigMapModalVisible(false);
      fetchConfigMaps();
    } catch (error: any) {
      console.error('Form submission failed:', error);
      message.error('提交失败: ' + (error.response?.data?.error || error.message || '未知错误'));
    }
  };

  const handleShowDetails = (data: Record<string, string>, isSecretData: boolean = false) => {
    setCurrentData(data);
    setIsSecret(isSecretData);
    setDetailModalVisible(true);
  };

  const handleAddDataItem = async () => {
    try {
      const values = await dataForm.validateFields();
      const newData = { ...currentData, [values.key]: values.value };
      setCurrentData(newData);
      dataForm.resetFields();
    } catch (error) {
      console.error('Failed to add data item:', error);
    }
  };

  const handleRemoveDataItem = (key: string) => {
    const newData = { ...currentData };
    delete newData[key];
    setCurrentData(newData);
  };

  const handleEditItem = (key: string, value: string) => {
    setEditingKey(key);
    setEditingValue(value);
  };

  const handleSaveEdit = () => {
    if (editingKey) {
      const newData = { ...currentData };
      delete newData[editingKey];
      newData[editingKey] = editingValue;
      setCurrentData(newData);
      setEditingKey('');
      setEditingValue('');
    }
  };

  const handleCancelEdit = () => {
    setEditingKey('');
    setEditingValue('');
  };

  const secretColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '命名空间',
      dataIndex: 'namespace',
      key: 'namespace',
    },
    {
      title: '数据项数量',
      key: 'dataCount',
      render: (text: string, record: Secret) => Object.keys(record.data || {}).length,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: Secret) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleShowDetails(record.data, true)}
            title="查看数据"
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleAddOrEditSecret(record)}
            title="编辑"
          />
          <Popconfirm
            title="确定要删除这个Secret吗？"
            onConfirm={() => handleDeleteSecret(record.name, record.namespace)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />} title="删除" />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const configMapColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '命名空间',
      dataIndex: 'namespace',
      key: 'namespace',
    },
    {
      title: '数据项数量',
      key: 'dataCount',
      render: (text: string, record: ConfigMap) => Object.keys(record.data || {}).length,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: ConfigMap) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleShowDetails(record.data)}
            title="查看数据"
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleAddOrEditConfigMap(record)}
            title="编辑"
          />
          <Popconfirm
            title="确定要删除这个ConfigMap吗？"
            onConfirm={() => handleDeleteConfigMap(record.name, record.namespace)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" danger icon={<DeleteOutlined />} title="删除" />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const dataColumns = [
    {
      title: '键',
      dataIndex: 'key',
      key: 'key',
      render: (text: string, record: { key: string, value: string }) => (
        editingKey === record.key ? (
          <Input
            value={editingKey}
            onChange={(e) => setEditingKey(e.target.value)}
            style={{ width: '100%' }}
          />
        ) : (
          <div
            style={{ cursor: 'pointer' }}
            onClick={() => handleEditItem(record.key, record.value)}
          >
            {text}
          </div>
        )
      ),
    },
    {
      title: '值',
      dataIndex: 'value',
      key: 'value',
      ellipsis: true,
      render: (text: string, record: { key: string, value: string }) => {
        // 如果是编辑状态
        if (editingKey === record.key) {
          return (
            <TextArea
              value={editingValue}
              onChange={(e) => setEditingValue(e.target.value)}
              autoSize={{ minRows: 2, maxRows: 6 }}
              style={{ width: '100%' }}
            />
          );
        }

        // 如果是Secret数据，但我们已经在handleAddOrEditSecret中解码了数据
        // 所以这里直接显示，不需要再次解码

        // 普通ConfigMap数据
        return (
          <Tooltip title="点击编辑">
            <div
              style={{ cursor: 'pointer', whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}
              onClick={() => handleEditItem(record.key, text)}
            >
              {text}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any, record: { key: string }) => (
        <Space>
          {editingKey === record.key ? (
            <>
              <Button
                type="text"
                icon={<SaveOutlined />}
                onClick={handleSaveEdit}
                title="保存"
              />
              <Button
                type="text"
                danger
                onClick={handleCancelEdit}
                title="取消"
              >
                取消
              </Button>
            </>
          ) : (
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleRemoveDataItem(record.key)}
              title="删除"
            />
          )}
        </Space>
      ),
    },
  ];

  const dataSource = Object.entries(currentData).map(([key, value]) => ({
    key,
    value,
  }));

  // 获取命名空间列表
  const fetchNamespaces = async (clusterName: string) => {
    try {
      const response = await clusterApi.getNamespaces(clusterName);
      if (response.data && response.data.data) {
        setNamespaces(response.data.data);
        // 如果当前选择的命名空间不在列表中，则设置为第一个命名空间
        if (response.data.data.length > 0 && !response.data.data.includes(selectedNamespace)) {
          setSelectedNamespace(response.data.data[0]);
        }
      } else {
        // 如果API返回空数据，使用默认值
        setNamespaces(['default']);
        setSelectedNamespace('default');
      }
    } catch (error: any) {
      console.error('Failed to fetch namespaces:', error);
      message.error('获取命名空间列表失败: ' + (error.response?.data?.error || error.message || '未知错误'));
      setNamespaces(['default']);
      setSelectedNamespace('default');
    }
  };

  // 处理集群变更
  const handleClusterChange = (value: string) => {
    setSelectedCluster(value);
    // 重置命名空间为default
    setSelectedNamespace('default');
    // 获取新集群的命名空间列表
    fetchNamespaces(value);
  };

  // 处理命名空间变更
  const handleNamespaceChange = (value: string) => {
    setSelectedNamespace(value);
  };

  return (
    <AppLayout>
      <div style={{ marginBottom: 16, display: 'flex', gap: '16px' }}>
        <Select
          style={{ width: 200 }}
          placeholder="选择集群"
          value={selectedCluster}
          onChange={handleClusterChange}
        >
          {clusters.map(cluster => (
            <Option key={cluster.name} value={cluster.name}>
              {cluster.name} {cluster.is_default && '(默认)'}
            </Option>
          ))}
        </Select>

        <Select
          style={{ width: 200 }}
          placeholder="选择命名空间"
          value={selectedNamespace}
          onChange={handleNamespaceChange}
        >
          {namespaces.map(ns => (
            <Option key={ns} value={ns}>{ns}</Option>
          ))}
        </Select>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Secrets" key="secrets">
          <div style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => handleAddOrEditSecret()}
              disabled={!selectedCluster || !selectedNamespace}
            >
              新建Secret
            </Button>
          </div>

          <Table
            columns={secretColumns}
            dataSource={secrets}
            rowKey={(record) => `${record.namespace}-${record.name}`}
            loading={loading && activeTab === 'secrets'}
            pagination={{ pageSize: 10 }}
          />
        </TabPane>

        <TabPane tab="ConfigMaps" key="configmaps">
          <div style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => handleAddOrEditConfigMap()}
              disabled={!selectedCluster || !selectedNamespace}
            >
              新建ConfigMap
            </Button>
          </div>

          <Table
            columns={configMapColumns}
            dataSource={configMaps}
            rowKey={(record) => `${record.namespace}-${record.name}`}
            loading={loading && activeTab === 'configmaps'}
            pagination={{ pageSize: 10 }}
          />
        </TabPane>
      </Tabs>

      {/* Secret Modal */}
      <Modal
        title={currentSecret ? '编辑Secret' : '新建Secret'}
        open={secretModalVisible}
        onOk={handleSecretSubmit}
        onCancel={() => setSecretModalVisible(false)}
        width={800}
        destroyOnClose
      >
        <Form form={secretForm} layout="vertical">
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入名称' }]}
          >
            <Input disabled={!!currentSecret} />
          </Form.Item>
          <Form.Item
            name="namespace"
            label="命名空间"
            rules={[{ required: true, message: '请选择命名空间' }]}
            initialValue={selectedNamespace}
          >
            <Select disabled={!!currentSecret}>
              {namespaces.map(ns => (
                <Option key={ns} value={ns}>{ns}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="cluster"
            label="集群"
            initialValue={selectedCluster}
          >
            <Select disabled>
              {clusters.map(cluster => (
                <Option key={cluster.name} value={cluster.name}>
                  {cluster.name} {cluster.is_default && '(默认)'}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>

        <Card title="数据" style={{ marginTop: 16 }}>
          <Form form={dataForm} layout="inline" onFinish={handleAddDataItem}>
            <Form.Item
              name="key"
              rules={[{ required: true, message: '请输入键' }]}
              style={{ width: '40%' }}
            >
              <Input placeholder="键" />
            </Form.Item>
            <Form.Item
              name="value"
              rules={[{ required: true, message: '请输入值' }]}
              style={{ width: '40%' }}
            >
              <TextArea
                placeholder="值"
                autoSize={{ minRows: 2, maxRows: 6 }}
              />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                添加
              </Button>
            </Form.Item>
          </Form>

          <Table
            columns={dataColumns}
            dataSource={dataSource}
            rowKey="key"
            pagination={false}
            style={{ marginTop: 16 }}
          />
        </Card>
      </Modal>

      {/* ConfigMap Modal */}
      <Modal
        title={currentConfigMap ? '编辑ConfigMap' : '新建ConfigMap'}
        open={configMapModalVisible}
        onOk={handleConfigMapSubmit}
        onCancel={() => setConfigMapModalVisible(false)}
        width={800}
        destroyOnClose
      >
        <Form form={configMapForm} layout="vertical">
          <Form.Item
            name="name"
            label="名称"
            rules={[{ required: true, message: '请输入名称' }]}
          >
            <Input disabled={!!currentConfigMap} />
          </Form.Item>
          <Form.Item
            name="namespace"
            label="命名空间"
            rules={[{ required: true, message: '请选择命名空间' }]}
            initialValue={selectedNamespace}
          >
            <Select disabled={!!currentConfigMap}>
              {namespaces.map(ns => (
                <Option key={ns} value={ns}>{ns}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="cluster"
            label="集群"
            initialValue={selectedCluster}
          >
            <Select disabled>
              {clusters.map(cluster => (
                <Option key={cluster.name} value={cluster.name}>
                  {cluster.name} {cluster.is_default && '(默认)'}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>

        <Card title="数据" style={{ marginTop: 16 }}>
          <Form form={dataForm} layout="inline" onFinish={handleAddDataItem}>
            <Form.Item
              name="key"
              rules={[{ required: true, message: '请输入键' }]}
              style={{ width: '40%' }}
            >
              <Input placeholder="键" />
            </Form.Item>
            <Form.Item
              name="value"
              rules={[{ required: true, message: '请输入值' }]}
              style={{ width: '40%' }}
            >
              <TextArea
                placeholder="值"
                autoSize={{ minRows: 2, maxRows: 6 }}
              />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                添加
              </Button>
            </Form.Item>
          </Form>

          <Table
            columns={dataColumns}
            dataSource={dataSource}
            rowKey="key"
            pagination={false}
            style={{ marginTop: 16 }}
          />
        </Card>
      </Modal>

      {/* Detail Modal */}
      <Modal
        title={isSecret ? "Secret数据详情" : "ConfigMap数据详情"}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        <Table
          columns={dataColumns}
          dataSource={dataSource}
          rowKey="key"
          pagination={false}
        />
      </Modal>
    </AppLayout>
  );
};

export default Configs;
