import React, { useState } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, theme } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ProjectOutlined,
  CloudUploadOutlined,
  SettingOutlined,
  GlobalOutlined,
  CalendarOutlined,
  UserOutlined,
  LogoutOutlined,
  ClusterOutlined,
  LockOutlined,
} from '@ant-design/icons';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const { Header, Sider, Content } = Layout;

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const { token: { colorBgContainer, borderRadiusLG } } = theme.useToken();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const items = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed} theme="light">
        <div style={{ height: 32, margin: 16, background: 'rgba(0, 0, 0, 0.2)', borderRadius: 6 }} />
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={[
            {
              key: '/projects',
              icon: <ProjectOutlined />,
              label: <Link to="/projects">项目管理</Link>,
            },
            {
              key: '/releases',
              icon: <CloudUploadOutlined />,
              label: <Link to="/releases">发布管理</Link>,
            },
            {
              key: '/plans',
              icon: <CalendarOutlined />,
              label: <Link to="/plans">计划管理</Link>,
            },
            {
              key: '/configs',
              icon: <SettingOutlined />,
              label: <Link to="/configs">配置管理</Link>,
            },
            {
              key: '/routes',
              icon: <GlobalOutlined />,
              label: <Link to="/routes">路由管理</Link>,
            },
            {
              key: '/clusters',
              icon: <ClusterOutlined />,
              label: <Link to="/clusters">集群管理</Link>,
            },
            {
              key: '/roles',
              icon: <LockOutlined />,
              label: <Link to="/roles">权限管理</Link>,
            },
          ]}
        />
      </Sider>
      <Layout>
        <Header style={{ padding: 0, background: colorBgContainer }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingRight: 24 }}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: '16px', width: 64, height: 64 }}
            />
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span style={{ marginRight: 12 }}>欢迎，{user?.username || '用户'}</span>
              <Dropdown menu={{ items }} placement="bottomRight">
                <Avatar icon={<UserOutlined />} style={{ cursor: 'pointer' }} />
              </Dropdown>
            </div>
          </div>
        </Header>
        <Content
          style={{
            margin: '24px 16px',
            padding: 24,
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
            minHeight: 280,
            overflow: 'auto',
          }}
        >
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default AppLayout;
