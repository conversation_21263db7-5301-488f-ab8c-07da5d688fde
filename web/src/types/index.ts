// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  fullName?: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  password: string;
  email: string;
  fullName?: string;
}

// 项目相关类型
export interface Project {
  id: number;
  project_name: string;
  project_describe: string;
  chart_repo_name: string;
  chart_repo_url: string;
  chart_name: string;
  creater: string;
  create_time: number;
  update_time: number;
  deleted: boolean;
}

export interface ProjectTag {
  id: number;
  project_id: number;
  tag: string;
  create_time: number;
}

// 发布相关类型
export interface Release {
  id: number;
  project_id: number;
  release_name: string;
  cluster_name: string;
  cluster_namespace: string;
  chart_version: string;
  chart_values: string;
  release_env: number;
  creater: string;
  create_time: number;
  update_time: number;
}

export interface ReleaseHistory {
  id: number;
  project_id: number;
  release_id: number;
  release_name: string;
  prev_chart_version: string;
  chart_version: string;
  prev_chart_values: string;
  chart_values: string;
  prev_image_tag: string;
  image_tag: string;
  username: string;
  change_log: string;
  h_type: string;
  create_time: number;
}

// 计划相关类型
export interface ReleasePlan {
  id: number;
  plan_name: string;
  plan_status: string;
  plan_start_time: number;
  plan_end_time: number;
  creater: string;
  updater: string;
  create_time: number;
  update_time: number;
}

export interface PlanItem {
  id: number;
  plan_id: number;
  project_id: number;
  item_type: string;
  item_seq: number;
  item_status: string;
  ding_audit_id: string;
  creater: string;
  create_time: number;
  updater: string;
  update_time: number;
  issue_no: string;
  tester: string;
  pm_name: string;
  byroad_change: string;
  meman_change: string;
  db_change: string;
  dove_change: string;
  other_change: string;
  comment: string;
}

export interface PlanItemRelease {
  id: number;
  plan_item_id: number;
  release_id: number;
  target_tag: string;
  rollback_tag: string;
  release_status: string;
  err_msg: string;
  change_log: string;
  create_time: number;
  update_time: number;
}

// 配置相关类型
export interface Secret {
  name: string;
  namespace: string;
  data: Record<string, string>;
}

export interface ConfigMap {
  name: string;
  namespace: string;
  data: Record<string, string>;
}

// 路由相关类型
export interface Route {
  id: string;
  name: string;
  uri: string;
  host: string;
  upstream_id: string;
  methods: string[];
}

export interface Upstream {
  id: string;
  name: string;
  type: string;
  nodes: UpstreamNode[];
}

export interface UpstreamNode {
  host: string;
  port: number;
  weight: number;
}

// 集群相关类型
export interface Cluster {
  name: string;
  server: string;
  status: string;
  version?: string;
  node_count?: number;
  is_default: boolean;
}

// 通用响应类型
export interface ApiResponse<T> {
  data: T;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  page_size: number;
}
