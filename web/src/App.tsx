import React from 'react';
import { BrowserRouter as Router, Routes as RouterRoutes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import { AuthProvider } from './contexts/AuthContext';
import PrivateRoute from './components/PrivateRoute';
import Login from './pages/Login';
import Profile from './pages/Profile';
import Projects from './pages/Projects';
import Releases from './pages/Releases';
import Plans from './pages/Plans';
import Configs from './pages/Configs';
import RouteManagement from './pages/Routes';
import Clusters from './pages/Clusters';
import RoleManagement from './pages/RoleManagement';
import './App.css';

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <AuthProvider>
        <Router>
          <RouterRoutes>
            <Route path="/login" element={<Login />} />
            <Route path="/projects" element={
              <PrivateRoute>
                <Projects />
              </PrivateRoute>
            } />
            <Route path="/releases" element={
              <PrivateRoute>
                <Releases />
              </PrivateRoute>
            } />
            <Route path="/plans" element={
              <PrivateRoute>
                <Plans />
              </PrivateRoute>
            } />
            <Route path="/configs" element={
              <PrivateRoute>
                <Configs />
              </PrivateRoute>
            } />
            <Route path="/routes" element={
              <PrivateRoute>
                <RouteManagement />
              </PrivateRoute>
            } />
            <Route path="/clusters" element={
              <PrivateRoute>
                <Clusters />
              </PrivateRoute>
            } />
            <Route path="/profile" element={
              <PrivateRoute>
                <Profile />
              </PrivateRoute>
            } />
            <Route path="/roles" element={
              <PrivateRoute>
                <RoleManagement />
              </PrivateRoute>
            } />
            <Route path="/" element={<Navigate to="/projects" replace />} />
          </RouterRoutes>
        </Router>
      </AuthProvider>
    </ConfigProvider>
  );
};

export default App;
