body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubunt<PERSON>', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 覆盖一些Ant Design样式 */
.ant-layout {
  min-height: 100vh;
}

.ant-layout-header {
  background: #fff;
  padding: 0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.ant-layout-content {
  margin: 24px 16px;
  padding: 24px;
  background: #fff;
  min-height: 280px;
}

.ant-layout-sider-children {
  display: flex;
  flex-direction: column;
}

.logo {
  height: 32px;
  margin: 16px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
}

.site-layout-background {
  background: #fff;
}

.ant-table-wrapper {
  width: 100%;
  overflow-x: auto;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ant-layout-content {
    margin: 12px 8px;
    padding: 12px;
  }
}
