# 基于ArgoCD+Helm+ChartMuseum的应用发布管理系统设计文档

## 1. 系统概述

### 1.1 系统目标

设计并实现一个基于ArgoCD、He<PERSON>和Helm ChartMuseum的应用发布管理系统，提供完整的应用生命周期管理能力，包括：

1. 应用的发布管理（项目、Release、Chart）
2. 应用的Secret和ConfigMap管理
3. 应用的路由管理（集成APISIX）

### 1.2 系统架构

![系统架构图](https://placeholder-for-architecture-diagram.com)

#### 1.2.1 技术栈选择

- **后端**：Go语言
- **数据库**：MySQL
- **缓存**：Redis
- **前端**：React + Ant Design
- **CI/CD**：GitHub Actions
- **容器编排**：Kubernetes
- **应用部署**：ArgoCD
- **包管理**：Helm + ChartMuseum
- **API网关**：APISIX

#### 1.2.2 系统组件

1. **前端应用**：提供用户界面，与后端API交互
2. **后端API服务**：处理业务逻辑，与各组件集成
3. **MySQL数据库**：存储系统元数据
4. **Redis缓存**：提高系统性能，存储临时数据
5. **ArgoCD**：实现GitOps工作流，自动化应用部署
6. **Helm ChartMuseum**：存储和管理Helm Charts
7. **APISIX**：管理应用路由和API网关
8. **GitHub仓库**：存储应用Chart文件
9. **GitHub Actions**：自动化CI/CD流程

## 2. 功能设计

### 2.1 应用发布管理

#### 2.1.1 项目管理

- 创建、编辑、删除项目
- 项目权限管理
- 项目标签管理
- 项目与Chart仓库关联

#### 2.1.2 Release管理

- 创建、编辑、删除Release
- 多环境部署（开发、测试、生产）
- Release状态监控
- Release历史记录和回滚

#### 2.1.3 Chart管理

- Chart版本管理
- Chart模板定制
- Chart依赖管理
- Chart仓库集成

#### 2.1.4 发布计划

- 创建发布计划
- 计划项管理
- 发布顺序控制
- 审批流程集成（钉钉）

### 2.2 Secret和ConfigMap管理

- Secret创建、编辑、删除
- ConfigMap创建、编辑、删除
- 敏感信息加密存储
- 环境变量管理
- 配置模板管理
- 配置版本控制和回滚

### 2.3 路由管理（APISIX集成）

- API路由创建、编辑、删除
- 路由规则配置
- 流量控制策略
- 安全策略配置
- 监控和日志

### 2.4 用户和权限管理

- 用户认证与授权
- 角色管理
- 项目级权限控制
- 操作审计日志

### 2.5 CI/CD集成

- GitHub Actions工作流集成
- 自动化构建和测试
- Chart打包和发布
- 部署状态监控

## 3. 数据库设计

### 3.1 数据库表结构

#### 项目表 (k8swan_project)

```sql
create table k8swan_project (
    `id` int not null auto_increment comment '自增ID',
    `project_name` varchar(50) not null default '' comment '项目名字',
    `project_describe` varchar(100) not null default '' comment '项目说明',
    `chart_repo_name` varchar(50) not null default '' comment 'chart仓库名称',
    `chart_repo_url` varchar(50) not null default '' comment 'chart仓库地址',
    `chart_name` varchar(50) not null default '' comment 'chart名字',
    `creater` varchar(50) not null default '' comment '创建人',
    `create_time` int(11) not null default 0 comment '创建时间',
    `update_time` int(11) not null default 0 comment '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0',
    primary key (`id`),
    unique key `uniq_project_name` (`project_name`)
) comment '项目表';
```

#### 发布表 (k8swan_release)

```sql
create table k8swan_release (
    `id` int not null auto_increment comment '自增ID',
    `project_id` int not null default 0 comment '关联project ID',
    `release_name` varchar(50) not null default '' comment '发布名字',
    `cluster_name` varchar(50) not null default '' comment '发布的k8s集群名称',
    `cluster_namespace` varchar(50) not null default '' comment '项目发布到k8s的namespace',
    `chart_version` varchar(50) not null default '' comment 'chart项目版本',
    `chart_values` text comment 'release的values',
    `release_env` tinyint not null default 0 comment '发布环境',
    `creater` varchar(50) not null default '' comment '创建人',
    `create_time` int(11) not null default 0 comment '创建时间',
    `update_time` int(11) not null default 0 comment '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0',
    primary key (`id`),
    unique key `uniq_release_name_cluster_name` (`release_name`, `cluster_name`)
) comment '项目发布表';
```

#### 发布历史表 (k8swan_release_history)

```sql
create table k8swan_release_history (
    `id` int not null auto_increment comment '自增ID',
    `project_id` int not null default 0 comment '关联project ID',
    `release_id` int not null default 0 comment '关联release ID',
    `release_name` varchar(50) not null default '' comment 'release名字',
    `prev_chart_version` varchar(50) not null default '' comment '上一次发布的chart版本',
    `chart_version` varchar(50) not null default '' comment '本次chart项目版本',
    `prev_chart_values` text comment '上一次发布的values',
    `chart_values` text comment '本次发布的values',
    `prev_image_tag` varchar(50) comment '上一次发布镜像tag',
    `image_tag` varchar(50) comment '本次镜像tag',
    `username` varchar(50) not null default '' comment '发布者',
    `change_log` varchar(1024) not null default '' comment '发布说明',
    `h_type` varchar(30) not null default '' comment '日志类型',
    `create_time` int(11) not null default 0 comment '创建时间',
    primary key (`id`),
    key `idx_project_id_release_id` (`project_id`, `release_id`)
) comment '发布历史记录';
```

#### 用户项目关联表 (k8swan_user_project)

```sql
create table k8swan_user_project (
    `id` int not null auto_increment comment '自增ID',
    `username` varchar(20) not null default '' comment '用户名',
    `project_id` int not null default 0 comment '项目ID',
    `user_role` varchar(20) not null default '' comment '发布用户角色',
    `create_time` int(11) not null default 0 comment '创建时间',
    primary key (`id`),
    key `idx_username` (`username`)
) comment '用户项目关联表';
```

#### 操作日志表 (k8swan_action_log)

```sql
create table k8swan_action_log (
    `id` int not null auto_increment comment '自增ID',
    `username` varchar(50) not null default '' comment '操作用户',
    `action_code` varchar(50) not null default '' comment '操作行为',
    `project_id` int not null default 0 comment '操作项目ID',
    `release_id` varchar(50) not null default '' comment '操作release ID',
    `release_history_id` int not null default 0 comment '关联发布历史记录',
    `create_time` int(11) not null default 0 comment '创建时间',
    `project_name` varchar(50) not null default '' comment '项目名称',
    `release_name` varchar(50) not null default '' comment 'release名称',
    primary key (`id`),
    key `idx_project_id_release_id` (`project_id`, `release_id`),
    key `idx_username` (`username`)
) comment '用户操作日志';
```

#### 项目标签表 (project_tag)

```sql
create table project_tag(
    `id` int unsigned not null auto_increment comment 'id',
    `project_id` int not null default 0 comment '项目id',
    `tag` varchar(30) not null default '' comment 'tag',
    primary key(`id`),
    unique key `uniq_idx_p_t`(`tag`,`project_id`)
);
```

#### 发布计划表 (release_plan)

```sql
create table release_plan(
    `id` int unsigned not null auto_increment comment 'id',
    `plan_name` varchar(30) not null default '' comment '计划名字',
    `plan_status` varchar(20) not null default '' comment '计划状态',
    `plan_start_time` int(11) unsigned not null default 0 comment '计划开始执行时间',
    `creater` varchar(30) not null default '' comment '创建人',
    `updater` varchar(30) not null default '' comment '更新人',
    `create_time` int(11) not null default 0 comment '创建时间',
    `update_time` int(11) not null default 0 comment '更新时间',
    primary key(`id`)
) comment '发布计划表';
```

#### 计划项表 (plan_item)

```sql
create table plan_item(
    `id` int unsigned not null auto_increment comment 'id',
    `plan_id` int not null default 0 comment '所属计划id',
    `project_id` int not null default 0 comment '项目id',
    `item_type` varchar(20) not null default '' comment '发布项类型',
    `item_seq` int not null default 0 comment '发布顺序编号',
    `item_status` varchar(20) not null default '' comment '发布子项状态',
    `ding_audit_id` varchar(50) not null default '' comment '钉钉审核id',
    `creater` varchar(30) not null default '' comment '创建人',
    `create_time` int(11) not null default 0 comment '创建时间',
    `updater` varchar(30) not null default '' comment '更新人',
    `update_time` int(11) not null default 0 comment '更新时间',
    `deleted` bool not null default false comment '软删除标识',
    `issue_no` varchar(50) not null default '' comment '需求号',
    `tester` varchar(50) not null default '' comment '测试',
    `pm_name`  varchar(50) not null default '' comment '产品',
    `byroad_change` varchar(200) not null default '' comment '旁路变更',
    `meman_change` varchar(200) not null default '' comment '消息中心变更',
    `db_change` varchar(200) not null default '' comment '数据库变更',
    `dove_change` varchar(200) not null default '' comment 'dove变更',
    `other_change` varchar(200) not null default '' comment '其他变更',
    `comment` varchar(300) not null default '' comment '备注',
    key `idx_plan_id`(`plan_id`),
    primary key(`id`)
) comment '发布计划的子发布项目';
```

#### 计划项发布表 (plan_item_release)

```sql
create table plan_item_release(
    `id` int unsigned not null auto_increment comment 'id',
    `plan_item_id` int not null default 0 comment '发布计划项id',
    `release_id` int not null default 0 comment '要发布的release id',
    `target_tag` varchar(20) not null default '' comment '要发布到的目标镜像tag',
    `rollback_tag` varchar(20) not null default '' comment '回滚tag',
    `release_status` varchar(20) not null default '' comment 'helm release 发布状态',
    `err_msg` varchar(1024) not null default 0 comment '错误信息',
    `change_log` varchar(1024) not null default '' comment '发布说明',
    key `idx_item_id`(`plan_item_id`),
    primary key(`id`)
) comment '发布计划项的release id';
```

#### 钉钉用户ID表 (dingding_userid)

```sql
create table dingding_userid(
    `id` int unsigned not null auto_increment comment 'id',
    `phone` varchar(20) not null default '' comment '手机号',
    `auth_uid`  varchar(20) not null default '' comment 'auth中的id',
    `ding_user_id` varchar(60) not null default '' comment '钉钉userid',
    `create_time` int(11) not null default 0 comment '创建时间',
    `update_time` int(11) not null default 0 comment '更新时间',
    primary key(`id`),
    unique key `uniq_idx_phone`(`phone`)
) comment '钉钉用户id';
```

### 3.2 数据库关系图

![数据库关系图](https://placeholder-for-database-diagram.com)

## 4. 系统集成

### 4.1 GitHub Actions集成

#### 4.1.1 Chart仓库结构

```
charts-repo/
├── applications/
│   ├── app1/
│   │   ├── Chart.yaml
│   │   ├── values.yaml
│   │   ├── templates/
│   │   └── ...
│   ├── app2/
│   │   └── ...
│   └── ...
├── common/
│   ├── helpers/
│   └── templates/
└── .github/
    └── workflows/
        ├── lint.yaml
        ├── test.yaml
        └── publish.yaml
```

#### 4.1.2 Chart验证和发布工作流

```yaml
name: Helm Chart CI/CD

on:
  push:
    branches: [ main, master ]
    paths:
      - 'applications/**'
  pull_request:
    paths:
      - 'applications/**'

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Lint Helm Charts
        uses: helm/chart-testing-action@v2
        with:
          command: lint
          config: .github/ct.yaml

  package-and-publish:
    needs: lint
    if: github.event_name == 'push'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Helm
        uses: helm/helm-github-action@v1
        
      - name: Package Charts
        run: |
          mkdir -p .packaged/
          for chart in applications/*/; do
            helm package $chart -d .packaged/
          done
          
      - name: Push to ChartMuseum
        run: |
          for chart in .packaged/*.tgz; do
            curl -u ${{ secrets.CHARTMUSEUM_USER }}:${{ secrets.CHARTMUSEUM_PASSWORD }} \
              --data-binary "@$chart" \
              ${{ secrets.CHARTMUSEUM_URL }}/api/charts
          done
```

#### 4.1.3 应用CI/CD工作流

```yaml
name: Application CI/CD

on:
  push:
    branches: [ main, master ]
  pull_request:

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: '1.18'
          
      - name: Build and Test
        run: |
          go build ./...
          go test ./...
          
  deploy:
    needs: build-and-test
    if: github.event_name == 'push'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Charts Repo
        uses: actions/checkout@v3
        with:
          repository: your-org/charts-repo
          token: ${{ secrets.CHARTS_REPO_PAT }}
          
      - name: Update Application Version
        run: |
          # 更新Chart.yaml中的应用版本
          sed -i "s/appVersion:.*/appVersion: ${GITHUB_SHA::8}/" applications/your-app/Chart.yaml
          # 更新values.yaml中的镜像标签
          sed -i "s/tag:.*/tag: ${GITHUB_SHA::8}/" applications/your-app/values.yaml
          
      - name: Commit and Push Changes
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"
          git add applications/your-app/
          git commit -m "Update your-app to version ${GITHUB_SHA::8}"
          git push
```

### 4.2 ArgoCD集成

- 通过ArgoCD API创建和管理应用
- 监控应用部署状态
- 实现自动同步和手动同步
- 支持回滚操作

### 4.3 Helm ChartMuseum集成

- 通过ChartMuseum API上传和管理Chart
- 获取Chart版本列表
- 下载特定版本的Chart
- 删除Chart版本

### 4.4 APISIX集成

- 通过APISIX Admin API创建和管理路由
- 配置负载均衡和流量控制
- 设置安全策略
- 监控API调用情况

## 5. 系统安全

### 5.1 认证与授权

- JWT认证
- RBAC权限控制
- 项目级权限隔离
- 操作审计日志

### 5.2 数据安全

- 敏感信息加密存储
- 传输加密（HTTPS）
- 数据库访问控制
- 定期备份

### 5.3 操作安全

- 敏感操作二次确认
- 操作日志记录
- IP白名单
- 会话超时控制

## 6. 系统监控

### 6.1 应用监控

- Prometheus + Grafana监控
- 性能指标收集
- 健康检查
- 告警配置

### 6.2 日志管理

- 集中式日志收集
- 日志分析
- 异常检测
- 审计日志

## 7. 高可用设计

### 7.1 后端服务高可用

- 多副本部署
- 负载均衡
- 健康检查
- 自动恢复

### 7.2 数据库高可用

- MySQL主从复制
- 定期备份
- 故障自动切换
- 数据恢复机制

### 7.3 缓存高可用

- Redis哨兵模式
- 多节点部署
- 持久化配置
- 故障转移

## 8. 扩展性设计

### 8.1 水平扩展

- 无状态服务设计
- 基于Kubernetes的自动扩缩容
- 分布式缓存
- 数据库读写分离

### 8.2 功能扩展

- 插件机制
- Webhook支持
- API文档和SDK
- 自定义模板

## 9. 用户界面设计

### 9.1 主要页面

- 仪表盘/概览
- 项目管理
- 应用管理
- Release管理
- 配置管理
- 路由管理
- 用户/权限管理
- 系统设置

### 9.2 用户体验优化

- 响应式设计
- 操作向导
- 批量操作
- 实时状态更新
- 搜索和过滤

## 10. 部署架构

### 10.1 开发环境

- Docker Compose部署
- 本地开发工具链
- 模拟数据
- 热重载

### 10.2 测试环境

- Kubernetes部署
- CI/CD集成
- 自动化测试
- 性能测试

### 10.3 生产环境

- 多副本高可用部署
- 资源限制和请求
- 自动扩缩容
- 备份和恢复

## 11. 实施计划

### 11.1 阶段一：基础功能

- 项目管理
- Release管理
- Chart管理
- 用户认证与授权

### 11.2 阶段二：高级功能

- Secret和ConfigMap管理
- 路由管理（APISIX集成）
- 发布计划
- 审批流程

### 11.3 阶段三：优化和扩展

- 多环境支持
- 灰度发布
- 监控和告警
- 性能优化

## 12. 总结

本设计文档详细描述了基于ArgoCD、Helm和Helm ChartMuseum的应用发布管理系统的设计方案。系统采用Go语言开发后端，MySQL作为主数据库，Redis作为缓存，通过GitHub Actions实现CI/CD流程，集成ArgoCD、Helm ChartMuseum和APISIX，提供完整的应用生命周期管理能力。

系统设计充分考虑了功能需求、性能、安全性、可扩展性和用户体验，可以根据实际需求分阶段实施。
