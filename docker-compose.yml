services:
  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=motivus
      - MYSQL_ROOT_HOST=%  # 允许root用户从任何主机连接
      - TZ=Asia/Shanghai
    volumes:
      - mysql-data:/var/lib/mysql
      - ./deploy/mysql/init.sql:/docker-entrypoint-initdb.d/01-init.sql
    networks:
      - motivus-network
    command: --default-authentication-plugin=mysql_native_password --bind-address=0.0.0.0

  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - motivus-network

networks:
  motivus-network:
    driver: bridge

volumes:
  mysql-data:
  redis-data:
