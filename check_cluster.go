package main

import (
	"fmt"
	"log"

	"github.com/privasea/motivus/config"
	"github.com/privasea/motivus/models"
)

func main() {
	// Load configuration
	if err := config.LoadConfig("api/config/config.yaml"); err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// Initialize database
	if err := models.InitDB(); err != nil {
		log.Fatal("Failed to initialize database:", err)
	}

	// Query develop cluster
	var cluster models.Cluster
	if err := models.DB.Where("name = ?", "develop").First(&cluster).Error; err != nil {
		log.Fatal("Failed to find develop cluster:", err)
	}

	fmt.Printf("Cluster: %s\n", cluster.Name)
	fmt.Printf("Server: %s\n", cluster.Server)
	fmt.Printf("Token length: %d\n", len(cluster.Token))
	fmt.Printf("CA Data length: %d\n", len(cluster.CAData))
	fmt.Printf("Insecure: %v\n", cluster.Insecure)
	fmt.Printf("Is Default: %v\n", cluster.IsDefault)

	if cluster.Token != "" {
		fmt.Printf("Token (first 20 chars): %s...\n", cluster.Token[:min(20, len(cluster.Token))])
	} else {
		fmt.Println("No token configured!")
	}

	if cluster.CAData != "" {
		fmt.Printf("CA Data (first 50 chars): %s...\n", cluster.CAData[:min(50, len(cluster.CAData))])
	} else {
		fmt.Println("No CA data configured!")
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
