#!/bin/bash

# Create directories
mkdir -p deploy/mysql
mkdir -p deploy/apisix

# Check if k3d is installed
if ! command -v k3d &> /dev/null; then
    echo "k3d is not installed. Please install k3d first."
    echo "You can install it using: brew install k3d"
    exit 1
fi

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
    echo "kubectl is not installed. Please install kubectl first."
    echo "You can install it using: brew install kubectl"
    exit 1
fi

# Create k3d cluster if it doesn't exist
if ! k3d cluster list | grep -q "motivus"; then
    echo "Creating k3d cluster 'motivus'..."
    k3d cluster create motivus --api-port 6550 -p "8082:80@loadbalancer" --agents 1
else
    echo "k3d cluster 'motivus' already exists."
fi

# Set kubectl context to the k3d cluster
kubectl config use-context k3d-motivus

# Create namespaces
kubectl create namespace motivus --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace argocd --dry-run=client -o yaml | kubectl apply -f -

# Start Docker Compose services
echo "Starting Docker Compose services..."
docker-compose up -d

echo "Local environment setup complete!"
echo "You can access the API at http://localhost:8080"
echo "ChartMuseum is available at http://localhost:8081"
echo "APISIX is available at http://localhost:9080"
echo "APISIX Admin API is available at http://localhost:9180"
