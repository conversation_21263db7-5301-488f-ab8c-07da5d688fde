### 环境变量
@baseUrl = http://localhost:8080/api/v1
@token = 

### 注册用户
POST {{baseUrl}}/auth/register
Content-Type: application/json

{
  "username": "rd",
  "password": "admin123",
  "email": "<EMAIL>",
  "fullName": "Admin User"
}

### 用户登录
# @name login
POST {{baseUrl}}/auth/login
Content-Type: application/json

{
  "username": "rd",
  "password": "admin123"
}

### 保存登录返回的token
{% 
    if (login.response.body.token) {
        client.global.set("token", login.response.body.token);
    }
%}

### 获取用户信息
GET {{baseUrl}}/auth/profile
Authorization: Bearer {{token}}

### 更新用户信息
PUT {{baseUrl}}/auth/profile
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "fullName": "Updated Admin User",
  "email": "<EMAIL>"
}

### 创建项目
# @name createProject
POST {{baseUrl}}/projects
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "project_name": "测试项目",
  "project_describe": "这是一个测试项目",
  "chart_repo_name": "test-repo",
  "chart_repo_url": "http://chartmuseum:8080",
  "chart_name": "test-chart"
}

### 保存项目ID
{% 
    if (createProject.response.body.id) {
        client.global.set("projectId", createProject.response.body.id);
    }
%}

### 获取项目列表
GET {{baseUrl}}/projects
Authorization: Bearer {{token}}

### 获取单个项目
GET {{baseUrl}}/projects/{{projectId}}
Authorization: Bearer {{token}}

### 更新项目
PUT {{baseUrl}}/projects/{{projectId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "project_name": "更新后的测试项目",
  "project_describe": "这是一个更新后的测试项目",
  "chart_repo_name": "test-repo",
  "chart_repo_url": "http://chartmuseum:8080",
  "chart_name": "test-chart"
}

### 添加项目标签
POST {{baseUrl}}/projects/{{projectId}}/tags
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "tag": "v1.0.0"
}

### 获取项目标签
GET {{baseUrl}}/projects/{{projectId}}/tags
Authorization: Bearer {{token}}

### 创建发布
# @name createRelease
POST {{baseUrl}}/releases
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "project_id": "{{projectId}}",
  "release_name": "test-release",
  "cluster_name": "test-cluster",
  "cluster_namespace": "default",
  "chart_version": "1.0.0",
  "chart_values": "replicaCount: 1\nimage:\n  repository: nginx\n  tag: latest",
  "release_env": 1
}

### 保存发布ID
{% 
    if (createRelease.response.body.id) {
        client.global.set("releaseId", createRelease.response.body.id);
    }
%}

### 获取发布列表
GET {{baseUrl}}/releases
Authorization: Bearer {{token}}

### 获取单个发布
GET {{baseUrl}}/releases/{{releaseId}}
Authorization: Bearer {{token}}

### 更新发布
PUT {{baseUrl}}/releases/{{releaseId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "chart_version": "1.0.1",
  "chart_values": "replicaCount: 2\nimage:\n  repository: nginx\n  tag: stable"
}

### 部署发布
POST {{baseUrl}}/releases/{{releaseId}}/deploy
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "change_log": "初始部署"
}

### 获取发布历史
GET {{baseUrl}}/releases/{{releaseId}}/history
Authorization: Bearer {{token}}

### 创建发布计划
# @name createPlan
POST {{baseUrl}}/plans
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "plan_name": "测试发布计划",
  "plan_status": "draft",
  "plan_start_time": 1714147200
}

### 保存计划ID
{% 
    if (createPlan.response.body.id) {
        client.global.set("planId", createPlan.response.body.id);
    }
%}

### 获取计划列表
GET {{baseUrl}}/plans
Authorization: Bearer {{token}}

### 获取单个计划
GET {{baseUrl}}/plans/{{planId}}
Authorization: Bearer {{token}}

### 添加计划项
# @name createPlanItem
POST {{baseUrl}}/plans/{{planId}}/items
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "project_id": "{{projectId}}",
  "item_type": "deploy",
  "item_seq": 1,
  "item_status": "pending",
  "issue_no": "ISSUE-001",
  "tester": "tester",
  "pm_name": "PM"
}

### 保存计划项ID
{% 
    if (createPlanItem.response.body.id) {
        client.global.set("planItemId", createPlanItem.response.body.id);
    }
%}

### 获取计划项列表
GET {{baseUrl}}/plans/{{planId}}/items
Authorization: Bearer {{token}}

### 添加计划项发布
POST {{baseUrl}}/plans/items/{{planItemId}}/releases
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "release_id": "{{releaseId}}",
  "target_tag": "v1.0.0",
  "rollback_tag": "v0.9.0",
  "release_status": "pending",
  "change_log": "初始发布"
}

### 创建Secret
POST {{baseUrl}}/configs/secrets
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "test-secret",
  "namespace": "default",
  "data": {
    "username": "YWRtaW4=",
    "password": "cGFzc3dvcmQxMjM="
  }
}

### 获取Secret列表
GET {{baseUrl}}/configs/secrets
Authorization: Bearer {{token}}

### 创建ConfigMap
POST {{baseUrl}}/configs/configmaps
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "test-configmap",
  "namespace": "default",
  "data": {
    "config.json": "{\"key\": \"value\"}",
    "settings.yaml": "key: value"
  }
}

### 获取ConfigMap列表
GET {{baseUrl}}/configs/configmaps
Authorization: Bearer {{token}}

### 获取发布配置值
GET {{baseUrl}}/configs/releases/{{releaseId}}/values
Authorization: Bearer {{token}}

### 创建路由
POST {{baseUrl}}/routes
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "test-route",
  "uri": "/test",
  "host": "example.com",
  "upstream_id": "1",
  "methods": ["GET", "POST"]
}

### 获取路由列表
GET {{baseUrl}}/routes
Authorization: Bearer {{token}}

### 创建上游服务
POST {{baseUrl}}/routes/upstreams
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "test-upstream",
  "type": "roundrobin",
  "nodes": [
    {
      "host": "backend-service",
      "port": 8080,
      "weight": 1
    }
  ]
}

### 获取上游服务列表
GET {{baseUrl}}/routes/upstreams
Authorization: Bearer {{token}}

### 删除项目（谨慎操作）
# DELETE {{baseUrl}}/projects/{{projectId}}
# Authorization: Bearer {{token}}

### 删除发布（谨慎操作）
# DELETE {{baseUrl}}/releases/{{releaseId}}
# Authorization: Bearer {{token}}

### 删除计划（谨慎操作）
# DELETE {{baseUrl}}/plans/{{planId}}
# Authorization: Bearer {{token}}
