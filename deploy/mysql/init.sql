-- Create database if not exists
CREATE DATABASE IF NOT EXISTS motivus;
USE motivus;

-- Create tables
CREATE TABLE IF NOT EXISTS k8swan_project (
    `id` int not null auto_increment comment '自增ID',
    `project_name` varchar(50) not null default '' comment '项目名字',
    `project_describe` varchar(100) not null default '' comment '项目说明',
    `chart_repo_name` varchar(50) not null default '' comment 'chart仓库名称',
    `chart_repo_url` varchar(50) not null default '' comment 'chart仓库地址',
    `chart_name` varchar(50) not null default '' comment 'chart名字',
    `creater` varchar(50) not null default '' comment '创建人',
    `create_time` int(11) not null default 0 comment '创建时间',
    `update_time` int(11) not null default 0 comment '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0',
    primary key (`id`),
    unique key `uniq_project_name` (`project_name`)
) comment '项目表';

CREATE TABLE IF NOT EXISTS k8swan_release (
    `id` int not null auto_increment comment '自增ID',
    `project_id` int not null default 0 comment '关联project ID',
    `release_name` varchar(50) not null default '' comment '发布名字',
    `cluster_name` varchar(50) not null default '' comment '发布的k8s集群名称',
    `cluster_namespace` varchar(50) not null default '' comment '项目发布到k8s的namespace',
    `chart_version` varchar(50) not null default '' comment 'chart项目版本',
    `chart_values` text comment 'release的values',
    `release_env` tinyint not null default 0 comment '发布环境',
    `creater` varchar(50) not null default '' comment '创建人',
    `create_time` int(11) not null default 0 comment '创建时间',
    `update_time` int(11) not null default 0 comment '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0',
    primary key (`id`),
    unique key `uniq_release_name_cluster_name` (`release_name`, `cluster_name`)
) comment '项目发布表';

CREATE TABLE IF NOT EXISTS k8swan_release_history (
    `id` int not null auto_increment comment '自增ID',
    `project_id` int not null default 0 comment '关联project ID',
    `release_id` int not null default 0 comment '关联release ID',
    `release_name` varchar(50) not null default '' comment 'release名字',
    `prev_chart_version` varchar(50) not null default '' comment '上一次发布的chart版本',
    `chart_version` varchar(50) not null default '' comment '本次chart项目版本',
    `prev_chart_values` text comment '上一次发布的values',
    `chart_values` text comment '本次发布的values',
    `prev_image_tag` varchar(50) comment '上一次发布镜像tag',
    `image_tag` varchar(50) comment '本次镜像tag',
    `username` varchar(50) not null default '' comment '发布者',
    `change_log` varchar(1024) not null default '' comment '发布说明',
    `h_type` varchar(30) not null default '' comment '日志类型',
    `create_time` int(11) not null default 0 comment '创建时间',
    primary key (`id`),
    key `idx_project_id_release_id` (`project_id`, `release_id`)
) comment '发布历史记录';

CREATE TABLE IF NOT EXISTS k8swan_user_project (
    `id` int not null auto_increment comment '自增ID',
    `username` varchar(20) not null default '' comment '用户名',
    `project_id` int not null default 0 comment '项目ID',
    `user_role` varchar(20) not null default '' comment '发布用户角色',
    `create_time` int(11) not null default 0 comment '创建时间',
    primary key (`id`),
    key `idx_username` (`username`)
) comment '用户项目关联表';

CREATE TABLE IF NOT EXISTS k8swan_action_log (
    `id` int not null auto_increment comment '自增ID',
    `username` varchar(50) not null default '' comment '操作用户',
    `action_code` varchar(50) not null default '' comment '操作行为',
    `project_id` int not null default 0 comment '操作项目ID',
    `release_id` varchar(50) not null default '' comment '操作release ID',
    `release_history_id` int not null default 0 comment '关联发布历史记录',
    `create_time` int(11) not null default 0 comment '创建时间',
    `project_name` varchar(50) not null default '' comment '项目名称',
    `release_name` varchar(50) not null default '' comment 'release名称',
    primary key (`id`),
    key `idx_project_id_release_id` (`project_id`, `release_id`),
    key `idx_username` (`username`)
) comment '用户操作日志';

CREATE TABLE IF NOT EXISTS project_tag(
    `id` int unsigned not null auto_increment comment 'id',
    `project_id` int not null default 0 comment '项目id',
    `tag` varchar(30) not null default '' comment 'tag',
    primary key(`id`),
    unique key `uniq_idx_p_t`(`tag`,`project_id`)
);

CREATE TABLE IF NOT EXISTS release_plan(
    `id` int unsigned not null auto_increment comment 'id',
    `plan_name` varchar(30) not null default '' comment '计划名字',
    `plan_status` varchar(20) not null default '' comment '计划状态',
    `plan_start_time` int(11) unsigned not null default 0 comment '计划开始执行时间',
    `creater` varchar(30) not null default '' comment '创建人',
    `updater` varchar(30) not null default '' comment '更新人',
    `create_time` int(11) not null default 0 comment '创建时间',
    `update_time` int(11) not null default 0 comment '更新时间',
    primary key(`id`)
) comment '发布计划表';

CREATE TABLE IF NOT EXISTS plan_item(
    `id` int unsigned not null auto_increment comment 'id',
    `plan_id` int not null default 0 comment '所属计划id',
    `project_id` int not null default 0 comment '项目id',
    `item_type` varchar(20) not null default '' comment '发布项类型',
    `item_seq` int not null default 0 comment '发布顺序编号',
    `item_status` varchar(20) not null default '' comment '发布子项状态',
    `ding_audit_id` varchar(50) not null default '' comment '钉钉审核id',
    `creater` varchar(30) not null default '' comment '创建人',
    `create_time` int(11) not null default 0 comment '创建时间',
    `updater` varchar(30) not null default '' comment '更新人',
    `update_time` int(11) not null default 0 comment '更新时间',
    `deleted` bool not null default false comment '软删除标识',
    `issue_no` varchar(50) not null default '' comment '需求号',
    `tester` varchar(50) not null default '' comment '测试',
    `pm_name`  varchar(50) not null default '' comment '产品',
    `byroad_change` varchar(200) not null default '' comment '旁路变更',
    `meman_change` varchar(200) not null default '' comment '消息中心变更',
    `db_change` varchar(200) not null default '' comment '数据库变更',
    `dove_change` varchar(200) not null default '' comment 'dove变更',
    `other_change` varchar(200) not null default '' comment '其他变更',
    `comment` varchar(300) not null default '' comment '备注',
    key `idx_plan_id`(`plan_id`),
    primary key(`id`)
) comment '发布计划的子发布项目';

CREATE TABLE IF NOT EXISTS plan_item_release(
    `id` int unsigned not null auto_increment comment 'id',
    `plan_item_id` int not null default 0 comment '发布计划项id',
    `release_id` int not null default 0 comment '要发布的release id',
    `target_tag` varchar(20) not null default '' comment '要发布到的目标镜像tag',
    `rollback_tag` varchar(20) not null default '' comment '回滚tag',
    `release_status` varchar(20) not null default '' comment 'helm release 发布状态',
    `err_msg` varchar(1024) not null default '' comment '错误信息',
    `change_log` varchar(1024) not null default '' comment '发布说明',
    key `idx_item_id`(`plan_item_id`),
    primary key(`id`)
) comment '发布计划项的release id';

CREATE TABLE IF NOT EXISTS dingding_userid(
    `id` int unsigned not null auto_increment comment 'id',
    `phone` varchar(20) not null default '' comment '手机号',
    `auth_uid`  varchar(20) not null default '' comment 'auth中的id',
    `ding_user_id` varchar(60) not null default '' comment '钉钉userid',
    `create_time` int(11) not null default 0 comment '创建时间',
    `update_time` int(11) not null default 0 comment '更新时间',
    primary key(`id`),
    unique key `uniq_idx_phone`(`phone`)
) comment '钉钉用户id';

-- Insert sample data
INSERT INTO k8swan_project (project_name, project_describe, chart_repo_name, chart_repo_url, chart_name, creater, create_time, update_time)
VALUES ('sample-project', '示例项目', 'sample-repo', 'http://chartmuseum:8080', 'sample-chart', 'admin', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

INSERT INTO k8swan_release (project_id, release_name, cluster_name, cluster_namespace, chart_version, chart_values, release_env, creater, create_time, update_time)
VALUES (1, 'sample-release', 'local-cluster', 'default', '1.0.0', 'replicaCount: 1\nimage:\n  repository: nginx\n  tag: latest', 1, 'admin', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
