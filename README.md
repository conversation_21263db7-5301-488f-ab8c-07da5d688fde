# Motivus - 应用发布管理系统

Motivus 是一个基于 ArgoCD、Helm 和 Helm ChartMuseum 的应用发布管理系统，提供完整的应用生命周期管理能力。

## 功能特性

- **应用发布管理**：项目、Release、Chart 管理
- **配置管理**：Secret 和 ConfigMap 管理
- **路由管理**：集成 APISIX 的 API 路由管理
- **发布计划**：支持创建和执行发布计划
- **多环境支持**：开发、测试、生产环境管理
- **审批流程**：支持钉钉审批集成

## 技术栈

- **后端**：Go + Gin + GORM
- **数据库**：MySQL
- **缓存**：Redis
- **前端**：React + TypeScript + Ant Design
- **容器化**：Docker + Kubernetes
- **CI/CD**：GitHub Actions
- **应用部署**：ArgoCD
- **包管理**：Helm + ChartMuseum
- **API网关**：APISIX

## 快速开始

### 前提条件

- Docker 和 Docker Compose
- Go 1.18+
- MySQL 8.0+
- Redis 6.0+

### 本地开发

1. 克隆仓库

```bash
git clone https://github.com/privasea/motivus.git
cd motivus
```

2. 配置环境

修改 `api/config/config.yaml` 文件，配置数据库、Redis 等连接信息。

3. 启动服务

```bash
# 使用 Docker Compose 启动依赖服务
docker-compose up -d mysql redis

# 启动后端服务
cd api
go run main.go
```

4. 访问 API

API 服务默认运行在 http://localhost:8080

### 使用 Docker Compose 部署

```bash
docker-compose up -d
```

## API 文档

API 文档使用 Swagger 生成，可以通过访问 http://localhost:8080/swagger/index.html 查看。

## 项目结构

```
motivus/
├── api/                # 后端 API 服务
│   ├── config/         # 配置文件
│   ├── controllers/    # 控制器
│   ├── middleware/     # 中间件
│   ├── models/         # 数据模型
│   ├── pkg/            # 工具包
│   │   ├── apisix/     # APISIX 集成
│   │   ├── argocd/     # ArgoCD 集成
│   │   ├── helm/       # Helm 集成
│   │   └── utils/      # 工具函数
│   ├── routes/         # 路由配置
│   └── services/       # 业务逻辑
├── web/                # 前端应用
├── deploy/             # 部署配置
├── docs/               # 文档
└── scripts/            # 脚本
```

## 贡献指南

欢迎贡献代码，请遵循以下步骤：

1. Fork 仓库
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。
